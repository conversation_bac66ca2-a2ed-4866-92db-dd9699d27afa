/* eslint-disable @typescript-eslint/no-explicit-any */
import { Prisma, PrismaClient } from '@prisma/client';
import axios from 'axios';
import ApiException from '../utils/errorHandler';
const prisma = new PrismaClient();

export interface IHubSpotInvoice {
	id: string;
	amount: number;
	currency: string;
	contactId: string;
	companyId?: string;
	lineItems: IHsLineItem[];
	payment?: IPayment;
	status: string;
	contactDetails?: {
		email?: string;
		firstname?: string;
		lastname?: string;
		company?: string;
	};
	properties?: {
		hs_number?: string;
		deal_company_name?: string;
		contact_email?: string;
		contact_firstname?: string;
		contact_lastname?: string;
		contact_name?: string;
		contact_id?: string;
		deal_id?: string;
		company_id?: string;
		primary_company_name?: string;
		hs_recipient_company_address?: string;
		hs_recipient_company_address2?: string;
		hs_recipient_company_city?: string;
		hs_recipient_company_state?: string;
		hs_recipient_company_country?: string;
		hs_recipient_company_zip?: string;
		[key: string]: any;
	};
	associations?: {
		contacts?: {
			results: {
				id: string;
				type: string; // e.g., "invoice_to_contact"
			}[];
		};
		deals?: {
			results: {
				id: string;
				type: string; // e.g., "invoice_to_deal"
			}[];
		};
		quotes?: {
			results: {
				id: string;
				type: string; // e.g., "invoice_to_quote"
			}[];
		};
		['line items']?: {
			results: {
				id: string;
				type: string; // e.g., "invoice_to_line_item"
			}[];
		};
	};
	createdAt?: Date;
}

export interface IHsLineItem {
	id: string; // ← lineId / hubSpotLineId
	description?: string;
	quantity: number;
	unitPrice: number;
	total?: number; // ← for totalPrice
	subTotal?: number; // ← for subTotal
	sku?: string; // ← optional SKU mapping
	name?: string; // ← for productName
	hsProductId?: string; // ← for hubSpotProductId
	wooCommerceProductId?: string; // ← optional if syncing from Woo
	taxCode?: string;
	itemCode?: string;
}

export interface IInvoice {
	id: string;
	hubSpotInvoiceId: string;
	hubSpotContactId?: string;
	hubSpotDealId?: string;
	hubSpotQuoteId?: string;
	hubSpotInvoiceNumber?: string;
	wooCommerceAmount?: string;
	wooCommerceOrderDate?: Date;
	invoiceStatus?: string;
	synclogId: string;
	createdAt?: Date;
	updatedAt?: Date;
	InvoiceLines: IInvoiceLine[];
}

export interface IInvoiceLine {
	id: string;
	lineId?: string;
	invoiceId: string;
	description?: string;
	quantity?: number;
	unitPrice?: number;
	totalPrice?: number;
	subTotal?: number;
	wooCommerceProductId?: string;
	hubSpotProductId?: string;
	linnWorksInventoryId?: string;
	xeroItemId?: string;
	hubSpotLineId?: string;
	sku?: string;
	productName?: string;
	createdAt: Date;
	updatedAt: Date;
}

export interface IPayment {
	amount: number;
	date: string;
	reference?: string;
}

export interface IXeroInvoice {
	InvoiceID: string;
	AmountDue: number;
	Status: 'PAID' | 'DRAFT' | 'SUBMITTED';
	LineItems: IHsLineItem[];
	CurrencyCode: string;
}

const XERO_API_BASE =
	process.env.XERO_API_BASE || 'https://api.xero.com/api.xro/2.0';
const XERO_PAYMENT_CODE = process.env.XERO_PAYMENT_CODE || '304';
const XERO_ACCOUNT_CODE = process.env.XERO_ACCOUNT_CODE || '401';
const XERO_TAXTYPE_CODE = process.env.XERO_TAXTYPE_CODE || 'OUTPUT2';

// Xero API Rate Limiting Configuration
const XERO_RATE_LIMITS = {
	RETRY_DELAY_BASE: 1000, // Base delay in ms for exponential backoff
	MAX_RETRIES: 3, // Maximum number of retries for 429 errors
	MIN_REMAINING_CALLS: 5, // Minimum calls remaining before we start delaying
	PREVENTIVE_DELAY: 2000, // Delay when approaching limits (2 seconds)
	REQUEST_TIMEOUT: 300000, // 5 minutes timeout for long waits
	CONCURRENT_REQUEST_DELAY: 500, // Delay between concurrent requests
	RATE_LIMIT_RESET_DELAY: 60000, // 1 minute delay when hitting rate limits
};

// Track rate limit status from Xero headers
let lastRateLimitStatus = {
	dayRemaining: null as number | null,
	minRemaining: null as number | null,
	appMinRemaining: null as number | null,
	lastUpdated: 0,
	isRateLimited: false, // Track if we're currently rate limited
	rateLimitResetTime: 0, // When rate limit will reset
};

// Request queue to prevent concurrent API calls
let requestQueue: Promise<any> = Promise.resolve();
let activeRequests = 0;
const MAX_CONCURRENT_REQUESTS = 1; // Only allow 1 concurrent request to Xero

// Additional safety: Track last API call time to enforce minimum delays
let lastApiCallTime = 0;
const MIN_API_CALL_INTERVAL = 100; // Minimum 100ms between API calls

// Sync process tracking to prevent duplicate runs
let syncInProgress = false;
let syncStartTime: number | null = null;

// Configurable non-editable invoice statuses
const NON_EDITABLE_STATUSES = ['VOIDED', 'DELETED', 'PAID'];
// Note: We can always recreate any non-editable invoice since we have the data from HubSpot

/**
 * Build Xero address object from HubSpot invoice address properties
 * @param addressProperties - HubSpot invoice properties containing address fields
 * @returns Xero address object or undefined if no address data available
 */
function buildXeroAddress(addressProperties: any): any[] | undefined {
	if (!addressProperties) {
		return undefined;
	}

	const {
		hs_recipient_company_address,
		hs_recipient_company_address2,
		hs_recipient_company_city,
		hs_recipient_company_state,
		hs_recipient_company_country,
		hs_recipient_company_zip,
	} = addressProperties;

	// Check if any address field has a value
	const hasAddressData = [
		hs_recipient_company_address,
		hs_recipient_company_address2,
		hs_recipient_company_city,
		hs_recipient_company_state,
		hs_recipient_company_country,
		hs_recipient_company_zip,
	].some((field) => field && field.toString().trim().length > 0);

	if (!hasAddressData) {
		return undefined;
	}

	// Helper function to truncate and clean field values
	const cleanField = (
		value: any,
		maxLength: number,
		lettersOnly = false
	): string => {
		if (!value) return '';
		let cleaned = value.toString().trim();
		if (lettersOnly) {
			// Remove non-letter characters for country field
			cleaned = cleaned.replace(/[^A-Za-z\s]/g, '');
		}
		return cleaned.substring(0, maxLength);
	};

	// Build address according to Xero API specification
	const address = {
		AddressType: 'POBOX',
		AddressLine1: cleanField(hs_recipient_company_address, 500),
		AddressLine2: cleanField(hs_recipient_company_address2, 500),
		City: cleanField(hs_recipient_company_city, 255),
		Region: cleanField(hs_recipient_company_state, 255),
		PostalCode: cleanField(hs_recipient_company_zip, 50),
		Country: cleanField(hs_recipient_company_country, 50, true), // Letters only
	};

	return [address];
}

/**
 * @param hsProductId - The hs_product_id from HubSpot
 * @param itemName - The name/description of the item as fallback
 * @returns ItemCode for Xero
 */

function generateItemCode(
	hsProductId: string | null | undefined,
	itemName?: string | null
): string {
	// If we have a valid hs_product_id, use it
	if (hsProductId && hsProductId !== 'null' && hsProductId.trim().length > 0) {
		return `ITEM-${hsProductId}`;
	}

	// Fallback to item name, cleaned up for Xero
	if (itemName && itemName.trim().length > 0) {
		// Clean the name: remove special characters, spaces, and limit length
		const cleanName = itemName
			.trim()
			.toUpperCase()
			.replace(/[^A-Z0-9]/g, '') // Remove non-alphanumeric characters
			.substring(0, 20); // Limit to 20 characters

		return `ITEM-${cleanName}`;
	}

	// Last resort fallback
	return `ITEM-${Date.now()}`;
}

/**
 * Sleep function for rate limiting delays
 * @param ms - Milliseconds to sleep
 */
function sleep(ms: number): Promise<void> {
	return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * Calculate exponential backoff delay for retries
 * @param attempt - Current retry attempt (0-based)
 * @param baseDelay - Base delay in milliseconds
 * @returns Delay in milliseconds
 */
function calculateBackoffDelay(
	attempt: number,
	baseDelay: number = XERO_RATE_LIMITS.RETRY_DELAY_BASE
): number {
	// Exponential backoff: baseDelay * (2^attempt) + random jitter
	const exponentialDelay = baseDelay * Math.pow(2, attempt);
	const jitter = Math.random() * 1000; // Add up to 1 second of random jitter
	return Math.min(exponentialDelay + jitter, 30000); // Cap at 30 seconds
}

/**
 * Queue API requests to prevent concurrent calls that bypass rate limiting
 * @param apiCall - The API call function to execute
 * @returns Promise that resolves with the API call result
 */
async function queueApiRequest<T>(apiCall: () => Promise<T>): Promise<T> {
	// Wait for previous requests to complete
	const currentRequest = requestQueue.then(async () => {
		// Check if we're currently rate limited
		const now = Date.now();
		if (
			lastRateLimitStatus.isRateLimited &&
			now < lastRateLimitStatus.rateLimitResetTime
		) {
			const waitTime = lastRateLimitStatus.rateLimitResetTime - now;
			console.log(
				`⏳ Rate limited - waiting ${waitTime}ms before making API call`
			);
			await sleep(waitTime);
			// Reset rate limit status after waiting
			lastRateLimitStatus.isRateLimited = false;
			lastRateLimitStatus.rateLimitResetTime = 0;
		}

		// Limit concurrent requests
		while (activeRequests >= MAX_CONCURRENT_REQUESTS) {
			await sleep(XERO_RATE_LIMITS.CONCURRENT_REQUEST_DELAY);
		}

		// Enforce minimum interval between API calls
		const currentTime = Date.now();
		const timeSinceLastCall = currentTime - lastApiCallTime;
		if (timeSinceLastCall < MIN_API_CALL_INTERVAL) {
			const waitTime = MIN_API_CALL_INTERVAL - timeSinceLastCall;
			await sleep(waitTime);
		}

		activeRequests++;
		try {
			const result = await apiCall();
			lastApiCallTime = Date.now(); // Update last call time on success
			return result;
		} finally {
			activeRequests--;
		}
	});

	// Update the queue
	requestQueue = currentRequest.catch(() => {}); // Ignore errors in queue chain
	return currentRequest;
}

/**
 * Update rate limit status and set rate limit flags if needed
 * @param error - The 429 error response
 */
function updateRateLimitStatusFromError(error: any): void {
	const retryAfter = error.response?.headers?.['retry-after'];
	const rateLimitProblem = error.response?.headers?.['x-rate-limit-problem'];

	console.log(`🚫 Rate limit hit: ${rateLimitProblem || 'Unknown limit'}`);
	console.log(`🚫 Retry-After header: ${retryAfter || 'Not provided'}`);

	// Mark as rate limited
	lastRateLimitStatus.isRateLimited = true;

	// Calculate when rate limit will reset
	if (retryAfter) {
		// Retry-After is in seconds, convert to milliseconds and add buffer
		const retryAfterMs = parseInt(retryAfter) * 1000;
		lastRateLimitStatus.rateLimitResetTime = Date.now() + retryAfterMs + 1000; // Add 1s buffer
	} else {
		// If no Retry-After header, use default delay
		lastRateLimitStatus.rateLimitResetTime =
			Date.now() + XERO_RATE_LIMITS.RATE_LIMIT_RESET_DELAY;
	}

	// Reset rate limit counters to be safe
	lastRateLimitStatus.minRemaining = 0;
	lastRateLimitStatus.dayRemaining = null; // Don't reset daily limit as it might still be valid
	lastRateLimitStatus.appMinRemaining = 0;
	lastRateLimitStatus.lastUpdated = Date.now();
}

// Configurations and secrets - should be in environment variables
const HUBSPOT_API_KEY = process.env.HUBSPOT_API_KEY;
const XERO_CLIENT_ID = process.env.XERO_CLIENT_ID;
const XERO_CLIENT_SECRET = process.env.XERO_CLIENT_SECRET;
type XeroTokens = {
	accessToken: string;
	refreshToken: string;
	expiresAt: Date;
};

// Type helper for Xero API responses
type XeroApiResponse = {
	data?: any;
	headers?: Record<string, string>;
	status?: number;
};

// Fetch tokens from database
async function getXeroTokensFromDB(): Promise<XeroTokens> {
	// Fetch the first Connection record with xeroCredentials
	const connection = await prisma.connection.findFirst({
		where: {
			xeroCredentials: {
				not: Prisma.JsonNull,
			},
		},
	});

	if (!connection || !connection.xeroCredentials) {
		throw new ApiException({
			status: 500,
			message: 'Xero credentials not found in database',
			errorDescription:
				'Critical error: Xero credentials not found in database',
		});
	}

	const creds = connection.xeroCredentials as {
		access_token: string;
		refresh_token: string;
		expiresAt?: string | Date;
		expires_in?: number;
	};

	// Handle both possible field names for backward compatibility
	const accessToken = creds.access_token || (creds as any).accessToken;
	const refreshToken = creds.refresh_token || (creds as any).refreshToken;

	// Handle expiry date - could be stored as expiresAt or calculated from expires_in
	let expiresAt: Date;
	if (creds.expiresAt) {
		expiresAt = new Date(creds.expiresAt);
	} else if (creds.expires_in) {
		// If we have expires_in, calculate expiry from current time
		expiresAt = new Date(Date.now() + creds.expires_in * 1000);
	} else {
		// Default to current time (will trigger refresh)
		expiresAt = new Date();
	}

	return {
		accessToken,
		refreshToken,
		expiresAt,
	};
}

// Initialize tokens from DB
let XERO_ACCESS_TOKEN: string = '';
let XERO_REFRESH_TOKEN: string = '';
let XERO_TOKEN_EXPIRES_AT: Date = new Date();

// Token refresh cache to prevent redundant refreshes
let tokenRefreshPromise: Promise<void> | null = null;

// Flag to track if tokens have been initialized
let tokensInitialized: boolean = false;

// Initialize tokens lazily when needed
async function initializeTokens(): Promise<void> {
	if (tokensInitialized) {
		return;
	}

	try {
		const tokens: XeroTokens = await getXeroTokensFromDB();
		XERO_ACCESS_TOKEN = tokens.accessToken;
		XERO_REFRESH_TOKEN = tokens.refreshToken;
		XERO_TOKEN_EXPIRES_AT = tokens.expiresAt;
		tokensInitialized = true;
	} catch (error) {
		console.warn(
			'Xero credentials not found in database. They will need to be set up before using Xero sync.'
		);
		tokensInitialized = false;
	}
}

// Axios Instances
const hubspot = axios.create({
	baseURL: 'https://api.hubapi.com',
	params: { hapikey: HUBSPOT_API_KEY },
});

const xero = axios.create({
	baseURL: 'https://api.xero.com/api.xro/2.0',
	// Authorization header is now added dynamically in xeroApiCall function
});

// HubSpot API wrapper
const hubspotApi = {
	async getContact(contactId: string, connection: any) {
		console.log(`Fetching details for contact ID: ${contactId}`);

		if (!connection?.hubSpotCredentials)
			throw new ApiException({
				status: 500,
				message: 'Missing HubSpot credentials',
				errorDescription:
					'Critical error: HubSpot credentials not found in connection object',
			});
		const { hapikey } = connection.hubSpotCredentials as any;

		const response = await axios.get(
			`https://api.hubapi.com/crm/v3/objects/contacts/${contactId}?properties=email,firstname,lastname,company,phone,city,state,country,zip,address`,
			{
				headers: {
					Authorization: `Bearer ${hapikey}`,
				},
			}
		);
		console.log(`Successfully retrieved details for contact ID: ${contactId}`);
		return response.data;
	},

	async getDealAssociations(
		dealId: string,
		associations: string[],
		connection: any
	) {
		if (!connection?.hubSpotCredentials)
			throw new ApiException({
				status: 500,
				message: 'Missing HubSpot credentials',
				errorDescription:
					'Critical error: HubSpot credentials not found in connection object',
			});
		const { hapikey } = connection.hubSpotCredentials as any;

		const response = await axios.get(
			`https://api.hubapi.com/crm/v3/objects/deals/${dealId}/associations/${associations.join(
				','
			)}`,
			{
				headers: {
					Authorization: `Bearer ${hapikey}`,
				},
			}
		);
		return response.data?.results?.[0] || null;
	},

	async getQuoteAssociations(
		quoteId: string,
		associations: string[],
		connection: any
	) {
		if (!connection?.hubSpotCredentials)
			throw new ApiException({
				status: 500,
				message: 'Missing HubSpot credentials',
				errorDescription:
					'Critical error: HubSpot credentials not found in connection object',
			});
		const { hapikey } = connection.hubSpotCredentials as any;

		const response = await axios.get(
			`https://api.hubapi.com/crm/v3/objects/quotes/${quoteId}/associations/${associations.join(
				','
			)}`,
			{
				headers: {
					Authorization: `Bearer ${hapikey}`,
				},
			}
		);
		return response.data?.results?.[0] || null;
	},

	async getInvoiceQuoteAssociations(invoiceId: string, connection: any) {
		if (!connection?.hubSpotCredentials)
			throw new ApiException({
				status: 500,
				message: 'Missing HubSpot credentials',
				errorDescription:
					'Critical error: HubSpot credentials not found in connection object',
			});
		const { hapikey } = connection.hubSpotCredentials as any;

		console.log(`Fetching quote associations for invoice ${invoiceId}...`);

		try {
			const response = await axios.get(
				`https://api.hubapi.com/crm/v3/objects/invoice/${invoiceId}/associations/quote`,
				{
					headers: {
						Authorization: `Bearer ${hapikey}`,
					},
				}
			);

			const quoteId = response.data?.results?.[0]?.id || null;
			if (quoteId) {
				console.log(`Found quote ID ${quoteId} for invoice ${invoiceId}`);
			} else {
				console.log(`No quote associations found for invoice ${invoiceId}`);
			}

			return quoteId;
		} catch (error: any) {
			console.log(
				`No quote associations found for invoice ${invoiceId}: ${error.message}`
			);
			return null;
		}
	},

	async getInvoiceDealAssociations(invoiceId: string, connection: any) {
		if (!connection?.hubSpotCredentials)
			throw new ApiException({
				status: 500,
				message: 'Missing HubSpot credentials',
				errorDescription:
					'Critical error: HubSpot credentials not found in connection object',
			});
		const { hapikey } = connection.hubSpotCredentials as any;

		console.log(`Fetching deal associations for invoice ${invoiceId}...`);

		try {
			const response = await axios.get(
				`https://api.hubapi.com/crm/v3/objects/invoice/${invoiceId}/associations/deal`,
				{
					headers: {
						Authorization: `Bearer ${hapikey}`,
					},
				}
			);

			const dealId = response.data?.results?.[0]?.id || null;
			if (dealId) {
				console.log(`Found deal ID ${dealId} for invoice ${invoiceId}`);
			} else {
				console.log(`No deal associations found for invoice ${invoiceId}`);
			}

			return dealId;
		} catch (error: any) {
			console.log(
				`No deal associations found for invoice ${invoiceId}: ${error.message}`
			);
			return null;
		}
	},

	async getProduct(productId: string, connection: any) {
		if (!productId || productId === 'null') {
			console.log(`Fetching details for product ID: ${productId}`);
			console.log(`Error fetching product details: Not Found`);
			return null;
		}

		console.log(`Fetching details for product ID: ${productId}`);

		if (!connection?.hubSpotCredentials)
			throw new ApiException({
				status: 500,
				message: 'Missing HubSpot credentials',
				errorDescription:
					'Critical error: HubSpot credentials not found in connection object',
			});
		const { hapikey } = connection.hubSpotCredentials as any;

		const response = await axios.get(
			`https://api.hubapi.com/crm/v3/objects/products/${productId}?properties=name,description,price,hs_sku`,
			{
				headers: {
					Authorization: `Bearer ${hapikey}`,
				},
			}
		);
		console.log(`Successfully retrieved details for product ID: ${productId}`);
		return response.data;
	},

	async getInvoicePaymentAssociations(invoiceId: string, connection: any) {
		console.log(`Fetching payment associations for invoice ${invoiceId}...`);

		if (!connection?.hubSpotCredentials)
			throw new ApiException({
				status: 500,
				message: 'Missing HubSpot credentials',
				errorDescription:
					'Critical error: HubSpot credentials not found in connection object',
			});
		const { hapikey } = connection.hubSpotCredentials as any;

		try {
			const response = await axios.get(
				`https://api.hubapi.com/crm/v3/objects/invoice/${invoiceId}/associations/commerce_payments`,
				{
					headers: {
						Authorization: `Bearer ${hapikey}`,
					},
				}
			);

			const paymentIds =
				response.data?.results?.map((payment: any) => payment.id) || [];
			if (paymentIds.length > 0) {
				console.log(
					`Found ${paymentIds.length} payment association(s) for invoice ${invoiceId}`
				);
			} else {
				console.log(`No payment associations found for invoice ${invoiceId}`);
			}

			return paymentIds;
		} catch (error: any) {
			console.log(
				`No payment associations found for invoice ${invoiceId}: ${error.message}`
			);
			return [];
		}
	},

	async getCommercePaymentDetails(paymentId: string, connection: any) {
		console.log(`Fetching payment details for payment ID: ${paymentId}`);

		if (!connection?.hubSpotCredentials)
			throw new ApiException({
				status: 500,
				message: 'Missing HubSpot credentials',
				errorDescription:
					'Critical error: HubSpot credentials not found in connection object',
			});
		const { hapikey } = connection.hubSpotCredentials as any;

		try {
			const response = await axios.get(
				`https://api.hubapi.com/crm/v3/objects/commerce_payments/${paymentId}?properties=amount,amount_paid,hs_currency_code,date,status,method,reference,currency_code,bank_issuer,last_four_digits,hs_initial_amount,hs_customer_email,hs_createdate,hs_latest_status,hs_reference_number`,
				{
					headers: {
						Authorization: `Bearer ${hapikey}`,
					},
				}
			);

			console.log(
				`Successfully retrieved payment details for payment ID: ${paymentId}`
			);
			return response.data;
		} catch (error: any) {
			console.log(
				`Error fetching payment details for payment ID ${paymentId}: ${error.message}`
			);
			return null;
		}
	},

	async getInvoiceContactAssociations(invoiceId: string, connection: any) {
		console.log(`Fetching contact associations for invoice ${invoiceId}...`);

		if (!connection?.hubSpotCredentials)
			throw new ApiException({
				status: 500,
				message: 'Missing HubSpot credentials',
				errorDescription:
					'Critical error: HubSpot credentials not found in connection object',
			});
		const { hapikey } = connection.hubSpotCredentials as any;

		try {
			const response = await axios.get(
				`https://api.hubapi.com/crm/v3/objects/invoice/${invoiceId}/associations/contacts`,
				{
					headers: {
						Authorization: `Bearer ${hapikey}`,
					},
				}
			);

			const contactId = response.data?.results?.[0]?.id || null;
			if (contactId) {
				console.log(`Found contact ID ${contactId} for invoice ${invoiceId}`);
			} else {
				console.log(`No contact associations found for invoice ${invoiceId}`);
			}

			return contactId;
		} catch (error: any) {
			console.log(
				`No contact associations found for invoice ${invoiceId}: ${error.message}`
			);
			return null;
		}
	},

	async getProducts(productIds: string[], connection: any) {
		if (!productIds.length) return [];

		console.log(
			`Fetching details for ${productIds.length} products from HubSpot...`
		);

		// Log each product ID being fetched
		productIds.forEach((id) => {
			if (!id || id === 'null') {
				console.log(`Fetching details for product ID: ${id}`);
				console.log(`Error fetching product details: Not Found`);
			} else {
				console.log(`Fetching details for product ID: ${id}`);
			}
		});

		if (!connection?.hubSpotCredentials)
			throw new ApiException({
				status: 500,
				message: 'Missing HubSpot credentials',
				errorDescription:
					'Critical error: HubSpot credentials not found in connection object',
			});
		const { hapikey } = connection.hubSpotCredentials as any;

		// Filter out null/invalid product IDs
		const validProductIds = productIds.filter((id) => id && id !== 'null');

		if (validProductIds.length === 0) {
			console.log(`No valid product IDs to fetch`);
			return [];
		}

		const response = await axios.post(
			'https://api.hubapi.com/crm/v3/objects/products/batch/read',
			{
				properties: ['name', 'description', 'price', 'hs_sku'],
				inputs: validProductIds.map((id) => ({ id })),
			},
			{
				headers: {
					Authorization: `Bearer ${hapikey}`,
					'Content-Type': 'application/json',
				},
			}
		);

		// Log success for each product
		response.data.results?.forEach((product: any) => {
			console.log(
				`Successfully retrieved details for product ID: ${product.id}`
			);
		});

		console.log(
			`Successfully fetched ${
				response.data.results?.length || 0
			} product details`
		);
		return response.data.results || [];
	},
};

// Validate contact name requirements
function validateContactName(
	firstName: string,
	lastName: string,
	companyName: string
): { isValid: boolean; contactName: string; reason?: string } {
	const cleanFirstName = firstName?.trim() || '';
	const cleanLastName = lastName?.trim() || '';
	const cleanCompanyName = companyName?.trim() || '';

	// Check if we have at least one of: company name, first name, or last name
	if (!cleanCompanyName && !cleanFirstName && !cleanLastName) {
		return {
			isValid: false,
			contactName: '',
			reason: 'No company name, first name, or last name provided',
		};
	}

	// Priority 1: Use company name if available
	if (cleanCompanyName) {
		return {
			isValid: true,
			contactName: cleanCompanyName,
		};
	}

	// Priority 2: Use full name (first + last)
	if (cleanFirstName && cleanLastName) {
		return {
			isValid: true,
			contactName: `${cleanFirstName} ${cleanLastName}`.trim(),
		};
	}

	// Priority 3: Use first name only
	if (cleanFirstName) {
		return {
			isValid: true,
			contactName: cleanFirstName,
		};
	}

	// Priority 4: Use last name only
	if (cleanLastName) {
		return {
			isValid: true,
			contactName: cleanLastName,
		};
	}

	return {
		isValid: false,
		contactName: '',
		reason: 'All name fields are empty or invalid',
	};
}

// Find or create Xero contact function
async function findOrCreateXeroContact({
	email,
	firstName,
	lastName,
	companyName,
	connection,
	synclogId,
	invoiceProperties,
}: {
	email: string;
	firstName: string;
	lastName: string;
	companyName: string;
	connection: any;
	synclogId?: string;
	invoiceProperties?: any;
}): Promise<{ contactId: string | null; error?: string }> {
	// Log start of contact resolution
	if (synclogId) {
		await logSyncHistory(
			synclogId,
			'pending',
			`Starting contact resolution for ${email || 'unknown email'}`
		);
	}

	// Validate contact name requirements
	const nameValidation = validateContactName(firstName, lastName, companyName);

	if (!nameValidation.isValid) {
		const errorMessage = `Name validation fails - When all name fields (firstName, lastName, companyName) are empty or invalid: ${nameValidation.reason}`;
		console.error(errorMessage);
		console.error(`   - Email: ${email || 'N/A'}`);
		console.error(`   - First Name: ${firstName || 'N/A'}`);
		console.error(`   - Last Name: ${lastName || 'N/A'}`);
		console.error(`   - Company: ${companyName || 'N/A'}`);

		if (synclogId) {
			await logSyncHistory(synclogId, 'error', errorMessage, {
				email: email || 'N/A',
				firstName: firstName || 'N/A',
				lastName: lastName || 'N/A',
				companyName: companyName || 'N/A',
				reason: nameValidation.reason,
			});
		}
		return { contactId: null, error: errorMessage };
	}

	const contactName = nameValidation.contactName;
	console.log(`Contact validation passed. Using name: "${contactName}"`);

	if (synclogId) {
		await logSyncHistory(
			synclogId,
			'pending',
			`Creating contact for ${email} in Xero with name: ${contactName}`
		);
	}

	// Create new contact if not found
	const addresses = buildXeroAddress(invoiceProperties);
	const newContactPayload = {
		Name: contactName,
		...(email && { EmailAddress: email }),
		...(addresses && { Addresses: addresses }),
	};

	if (addresses) {
		console.log(
			`Adding address to Xero contact: ${addresses[0].AddressLine1 || ''}, ${
				addresses[0].City || ''
			}, ${addresses[0].PostalCode || ''}`
		);
	}
	console.log('Creating new Xero contact with payload:', newContactPayload);

	try {
		const createResponse = await xeroApiCall('post', '/Contacts', connection, {
			Contacts: [newContactPayload],
		});

		if (
			createResponse?.data?.Contacts &&
			Array.isArray(createResponse.data.Contacts) &&
			createResponse.data.Contacts.length > 0
		) {
			const newContactId = createResponse.data.Contacts[0].ContactID;
			const successMessage = `Contact for ${email} created in Xero with #: ${newContactId}`;
			console.log(
				`Successfully created new Xero contact: ${newContactId} with name: ${contactName}`
			);

			if (synclogId) {
				await logSyncHistory(synclogId, 'success', successMessage);
			}
			return { contactId: newContactId };
		}

		// If contact creation fails, log error and return null to continue processing
		const errorMessage =
			'Failed to create Xero contact: Invalid response structure';
		console.error(errorMessage);

		if (synclogId) {
			await logSyncHistory(synclogId, 'error', errorMessage, {
				response: createResponse?.data || 'No response data',
				contactPayload: newContactPayload,
			});
		}
		return { contactId: null, error: errorMessage };
	} catch (error: any) {
		// Handle Xero errors (validation, authentication, etc.)
		let errorMessage = 'Failed to create Xero contact';

		if (error.xeroValidationErrors) {
			// Use the structured error information from xeroApiCall
			const validationMessages = error.xeroValidationErrors.validationMessages;

			// Handle different types of Xero errors
			if (error.xeroValidationErrors.type === 'AuthenticationError') {
				errorMessage = `Xero authentication error: ${validationMessages.join(
					', '
				)}`;
			} else {
				errorMessage =
					validationMessages.length > 0
						? `Xero contact validation error: ${validationMessages.join(', ')}`
						: `Xero contact error: ${
								error.xeroValidationErrors.message || 'Unknown error'
						  }`;
			}

			if (synclogId) {
				await logSyncHistory(synclogId, 'error', errorMessage, {
					xeroValidationErrors: error.xeroValidationErrors,
					contactPayload: newContactPayload,
				});
			}
		} else {
			// Handle other types of errors (network, etc.)
			errorMessage = `Failed to create Xero contact: ${error.message}`;

			if (synclogId) {
				await logSyncHistory(synclogId, 'error', errorMessage, {
					error: error.message,
					httpStatus: error.response?.status,
					responseData: error.response?.data,
					contactPayload: newContactPayload,
				});
			}
		}

		console.error(errorMessage);
		return { contactId: null, error: errorMessage };
	}
}

// Xero Token Refresh Logic with caching to prevent redundant refreshes
async function refreshXeroToken(
	connection: any,
	force = false,
	synclogId?: string
): Promise<void> {
	// If there's already a refresh in progress, wait for it
	if (tokenRefreshPromise && !force) {
		await tokenRefreshPromise;
		return;
	}

	const now = new Date();

	if (!connection?.xeroCredentials) {
		const errorMessage = 'No Xero credentials found';
		if (synclogId) {
			await logSyncHistory(synclogId, 'error', errorMessage, {
				error: 'Xero credentials not found in connection object',
			});
		}
		throw new ApiException({
			status: 500,
			message: errorMessage,
			errorDescription:
				'Critical error: Xero credentials not found in connection object',
		});
	}

	// Ensure credentials are parsed
	let credentials = connection.xeroCredentials as any;
	if (typeof credentials === 'string') {
		credentials = JSON.parse(credentials);
	}

	const expiresAt = new Date(credentials.expiresAt);
	const expiresIn5Min = new Date(expiresAt.getTime() - 5 * 60 * 1000);

	if (!force && now < expiresIn5Min && XERO_ACCESS_TOKEN) {
		// Token is still valid and we have it in memory
		xero.defaults.headers.Authorization = `Bearer ${XERO_ACCESS_TOKEN}`;
		return;
	}

	// Create a promise for the refresh operation to prevent concurrent refreshes
	tokenRefreshPromise = (async () => {
		try {
			console.log('Refreshing Xero token...');

			if (synclogId) {
				await logSyncHistory(
					synclogId,
					'pending',
					'Starting Xero token refresh process'
				);
			}

			// Validate required credentials - these are critical errors that should stop the sync
			if (!credentials.refresh_token) {
				throw new ApiException({
					status: 500,
					message: 'Missing refresh_token in credentials',
					errorDescription:
						'Critical error: Xero refresh token not found in credentials',
				});
			}
			if (!XERO_CLIENT_ID || !XERO_CLIENT_SECRET) {
				throw new ApiException({
					status: 500,
					message:
						'Missing XERO_CLIENT_ID or XERO_CLIENT_SECRET environment variables',
					errorDescription:
						'Critical error: Xero client credentials not configured',
				});
			}

			// Additional validation for refresh token format
			if (
				typeof credentials.refresh_token !== 'string' ||
				credentials.refresh_token.length < 10
			) {
				throw new ApiException({
					status: 500,
					message:
						'Invalid refresh_token format - token appears to be malformed',
					errorDescription:
						'Critical error: Xero refresh token format is invalid',
				});
			}

			// Prepare the request payload
			const tokenData = new URLSearchParams();
			tokenData.append('grant_type', 'refresh_token');
			tokenData.append('refresh_token', credentials.refresh_token);
			const xeroScope = process.env.XERO_SCOPE;
			if (!xeroScope) {
				throw new ApiException({
					status: 500,
					message:
						'Xero integration is not fully configured. Please contact support.',
					errorDescription:
						'Critical error: XERO_SCOPE environment variable is missing. Xero scope not configured in environment variables.',
				});
			}
			tokenData.append('scope', xeroScope);

			// Create Basic Auth header
			const authString = `${XERO_CLIENT_ID}:${XERO_CLIENT_SECRET}`;
			const authHeader = `Basic ${Buffer.from(authString).toString('base64')}`;

			console.log('Making token refresh request to Xero...');

			const response = await axios.post(
				'https://identity.xero.com/connect/token',
				tokenData,
				{
					headers: {
						'Content-Type': 'application/x-www-form-urlencoded',
						Authorization: authHeader,
					},
					timeout: 30000, // 30 second timeout
				}
			);

			const { access_token, refresh_token, expires_in } = response.data;

			XERO_ACCESS_TOKEN = access_token;
			XERO_REFRESH_TOKEN = refresh_token;
			XERO_TOKEN_EXPIRES_AT = new Date(now.getTime() + expires_in * 1000);

			xero.defaults.headers.Authorization = `Bearer ${access_token}`;

			// Update credentials in DB - maintain the original field names
			const updatedCredentials = {
				...credentials,
				access_token: access_token,
				refresh_token: refresh_token,
				expiresAt: XERO_TOKEN_EXPIRES_AT.toISOString(),
				// Also update camelCase versions for backward compatibility
				accessToken: access_token,
				refreshToken: refresh_token,
			};

			await prisma.connection.update({
				where: { id: connection.id },
				data: {
					xeroCredentials: updatedCredentials,
				},
			});

			// Update the connection object in memory with fresh credentials
			connection.xeroCredentials = updatedCredentials;

			console.log('Xero token refreshed successfully');
			console.log('METRIC: xero_token_refreshed');

			if (synclogId) {
				await logSyncHistory(
					synclogId,
					'success',
					'Xero token refreshed successfully'
				);
			}
		} catch (error: any) {
			console.error('Failed to refresh Xero token:', error.message);

			// Log detailed error information for debugging
			if (error.response) {
				console.error('Xero API Error Response Status:', error.response.status);
				console.error(
					'Xero API Error Response Data:',
					JSON.stringify(error.response.data, null, 2)
				);
				console.error(
					'Xero API Error Response Headers:',
					JSON.stringify(error.response.headers, null, 2)
				);
			} else if (error.request) {
				console.error('No response received from Xero:', error.request);
			} else {
				console.error('Error setting up request:', error.message);
			}

			// Check for specific error types and provide helpful guidance
			let errorMessage = error.message;
			let helpfulMessage = '';

			if (error.response?.data?.error) {
				errorMessage = `${error.response.data.error}: ${
					error.response.data.error_description || 'Unknown error'
				}`;

				// Provide specific guidance based on error type
				if (error.response.data.error === 'invalid_grant') {
					helpfulMessage =
						' This usually means the refresh token has expired or been revoked. You may need to re-authorize the Xero connection.';
				} else if (error.response.data.error === 'invalid_client') {
					helpfulMessage =
						' This means the client credentials (XERO_CLIENT_ID/XERO_CLIENT_SECRET) are incorrect.';
				} else if (error.response.data.error === 'invalid_request') {
					helpfulMessage = ' The token refresh request format is invalid.';
				}
			}

			// Log token refresh failure to syncHistory
			if (synclogId) {
				await logSyncHistory(
					synclogId,
					'error',
					`Xero token refresh failed: ${errorMessage}${helpfulMessage}`,
					{
						error: errorMessage,
						helpfulMessage,
						response: error.response?.data,
						status: error.response?.status,
					}
				);
			}

			// Token refresh failure is critical - use ApiException to stop entire process
			throw new ApiException({
				status: 500,
				message: 'Xero token refresh failed',
				errorDescription: `Critical error: Unable to refresh Xero access token. ${errorMessage}${helpfulMessage}`,
			});
		} finally {
			// Clear the promise after completion
			tokenRefreshPromise = null;
		}
	})();

	await tokenRefreshPromise;
}

// Optimized wrapper for Xero API calls with intelligent token refresh and rate limiting
async function xeroApiCall(
	method: 'get' | 'post' | 'put' | 'delete',
	endpoint: string,
	connection: any,
	data?: any,
	retryAttempt: number = 0
): Promise<XeroApiResponse> {
	// Use request queue to prevent concurrent calls
	return queueApiRequest(async () => {
		// Enhanced proactive rate limiting based on Xero's response headers
		const now = Date.now();

		// Check if we're in a rate limited state
		if (
			lastRateLimitStatus.isRateLimited &&
			now < lastRateLimitStatus.rateLimitResetTime
		) {
			const waitTime = lastRateLimitStatus.rateLimitResetTime - now;
			console.log(
				` Currently rate limited - waiting ${waitTime}ms before API call`
			);
			await sleep(waitTime);
			lastRateLimitStatus.isRateLimited = false;
			lastRateLimitStatus.rateLimitResetTime = 0;
		}

		// Check minute rate limit
		if (
			lastRateLimitStatus.minRemaining !== null &&
			lastRateLimitStatus.minRemaining <= XERO_RATE_LIMITS.MIN_REMAINING_CALLS
		) {
			console.log(
				` Approaching minute limit (${lastRateLimitStatus.minRemaining} calls remaining), adding preventive delay`
			);
			await sleep(XERO_RATE_LIMITS.PREVENTIVE_DELAY);
		}

		// Check daily rate limit
		if (
			lastRateLimitStatus.dayRemaining !== null &&
			lastRateLimitStatus.dayRemaining <= XERO_RATE_LIMITS.MIN_REMAINING_CALLS
		) {
			console.log(
				` Approaching daily limit (${lastRateLimitStatus.dayRemaining} calls remaining), adding preventive delay`
			);
			await sleep(XERO_RATE_LIMITS.PREVENTIVE_DELAY);
		}

		// Check app minute rate limit
		if (
			lastRateLimitStatus.appMinRemaining !== null &&
			lastRateLimitStatus.appMinRemaining <=
				XERO_RATE_LIMITS.MIN_REMAINING_CALLS
		) {
			console.log(
				` Approaching app minute limit (${lastRateLimitStatus.appMinRemaining} calls remaining), adding preventive delay`
			);
			await sleep(XERO_RATE_LIMITS.PREVENTIVE_DELAY);
		}

		// Ensure we have a valid token before making the API call
		await ensureValidXeroToken(connection);

		// Get tenant ID from connection
		const credentials = connection.xeroCredentials as any;
		let tenantId = credentials.tenantId;

		// If tenant ID is missing, try to get it from other possible locations
		if (!tenantId) {
			tenantId =
				credentials.tenant_id ||
				credentials.organisationId ||
				credentials.organization_id;

			if (!tenantId) {
				console.error('Missing Xero tenant ID in credentials');
				console.error('Available credential keys:', Object.keys(credentials));
				throw new ApiException({
					status: 500,
					message: 'Missing Xero tenant ID',
					errorDescription:
						'Xero tenant ID is required for API calls but not found in credentials. Check your Xero app connection.',
				});
			} else {
				console.log(`Found tenant ID in alternative field: ${tenantId}`);
			}
		}

		// Helper function to build headers with current access token
		const buildHeaders = () => {
			const currentCredentials = connection.xeroCredentials as any;
			const token = currentCredentials.access_token;
			return {
				Authorization: `Bearer ${token}`,
				'Xero-tenant-id': tenantId,
				'Content-Type': 'application/json',
			};
		};

		// Prepare headers with current access token
		let headers = buildHeaders();

		// console.log(`Making Xero API call: ${method.toUpperCase()} ${endpoint}`);
		// console.log(`Using tenant ID: ${tenantId}`);

		try {
			let response;

			// Add timeout to prevent long waits from blocking the application
			const apiCallPromise = (async () => {
				if (method === 'get') {
					return await xero.get(endpoint, {
						headers,
						timeout: XERO_RATE_LIMITS.REQUEST_TIMEOUT,
					});
				} else if (method === 'post') {
					return await xero.post(endpoint, data, {
						headers,
						timeout: XERO_RATE_LIMITS.REQUEST_TIMEOUT,
					});
				} else if (method === 'put') {
					return await xero.put(endpoint, data, {
						headers,
						timeout: XERO_RATE_LIMITS.REQUEST_TIMEOUT,
					});
				} else if (method === 'delete') {
					return await xero.delete(endpoint, {
						headers,
						timeout: XERO_RATE_LIMITS.REQUEST_TIMEOUT,
					});
				}
			})();

			// Race between API call and timeout
			response = (await Promise.race([
				apiCallPromise,
				new Promise((_, reject) =>
					setTimeout(
						() =>
							reject(
								new Error(
									`Request timeout after ${XERO_RATE_LIMITS.REQUEST_TIMEOUT}ms`
								)
							),
						XERO_RATE_LIMITS.REQUEST_TIMEOUT
					)
				),
			])) as XeroApiResponse;

			// Update rate limit status from Xero response headers
			if (response && (response as any)?.headers) {
				const headers = (response as any).headers;
				const dayRemaining = headers['x-daylimit-remaining'];
				const minRemaining = headers['x-minlimit-remaining'];
				const appMinRemaining = headers['x-appminlimit-remaining'];

				// Update our tracking object with latest values from Xero
				if (dayRemaining !== undefined) {
					lastRateLimitStatus.dayRemaining = parseInt(dayRemaining);
				}
				if (minRemaining !== undefined) {
					lastRateLimitStatus.minRemaining = parseInt(minRemaining);
				}
				if (appMinRemaining !== undefined) {
					lastRateLimitStatus.appMinRemaining = parseInt(appMinRemaining);
				}
				lastRateLimitStatus.lastUpdated = Date.now();

				// Log current rate limit status
				if (dayRemaining || minRemaining || appMinRemaining) {
					console.log(
						` Rate limits - Day: ${dayRemaining || 'N/A'}, Min: ${
							minRemaining || 'N/A'
						}, AppMin: ${appMinRemaining || 'N/A'}`
					);
				}
			}

			// Add 2-second delay after successful Xero API call
			console.log(
				' Xero API call successful, waiting 2 seconds before next call...'
			);
			await sleep(2000);

			return response;
		} catch (error: any) {
			// Handle token expiry errors with a single retry
			console.log(
				` DEBUG: Caught error in xeroApiCall - Status: ${
					error.response?.status
				}, isTokenExpiredError: ${isTokenExpiredError(error)}`
			);

			if (isTokenExpiredError(error)) {
				console.log(
					'Token expired during API call, refreshing and retrying...'
				);
				console.log('Xero error details:', {
					status: error.response?.status,
					data: error.response?.data,
					endpoint: endpoint,
				});

				console.log(' About to refresh token...');
				await refreshXeroToken(connection, true);
				console.log(' Token refresh completed, rebuilding headers...');

				//  CRITICAL FIX: Rebuild headers with fresh access token after refresh
				headers = buildHeaders();
				const newToken = connection.xeroCredentials?.access_token;
				console.log(' Using refreshed token for retry...');
				console.log(` New token starts with: ${newToken?.substring(0, 20)}...`);

				// Additional debugging - check token expiry and scopes
				try {
					const tokenParts = newToken?.split('.');
					if (tokenParts && tokenParts.length >= 2) {
						const payload = JSON.parse(
							Buffer.from(tokenParts[1], 'base64').toString()
						);
						console.log(
							` Token expires at: ${new Date(payload.exp * 1000).toISOString()}`
						);
						console.log(` Token scopes: ${payload.scope || 'No scopes found'}`);
						console.log(` Current time: ${new Date().toISOString()}`);

						// Check if we have the required scope for deleting payments
						const scopes = payload.scope || '';
						const hasDeleteScope = scopes.includes(
							'accounting.transactions.delete'
						);
						const hasTransactionScope = scopes.includes(
							'accounting.transactions'
						);
						console.log(
							` Has accounting.transactions scope: ${hasTransactionScope}`
						);
						console.log(
							` Has accounting.transactions.delete scope: ${hasDeleteScope}`
						);

						if (
							!hasDeleteScope &&
							endpoint.includes('/Payments/') &&
							method === 'delete'
						) {
							console.error(
								` SCOPE ISSUE: Trying to delete payment but missing 'accounting.transactions.delete' scope!`
							);
							console.error(` Current scopes: ${scopes}`);
							console.error(` Required scope: accounting.transactions.delete`);
						}
					}
				} catch (tokenParseError) {
					console.log(' Could not parse token for debugging');
				}

				// Retry the API call once with updated headers containing fresh token
				console.log(
					` Retrying ${method.toUpperCase()} ${endpoint} with fresh token...`
				);
				try {
					let retryResponse: XeroApiResponse;
					if (method === 'get') {
						retryResponse = await xero.get(endpoint, { headers });
					} else if (method === 'post') {
						retryResponse = await xero.post(endpoint, data, { headers });
					} else if (method === 'put') {
						retryResponse = await xero.put(endpoint, data, { headers });
					} else if (method === 'delete') {
						retryResponse = await xero.delete(endpoint, { headers });
					} else {
						throw new Error(`Unsupported HTTP method: ${method}`);
					}

					// Add 3-second delay after successful retry
					console.log(
						'✅ Xero API retry successful, waiting 3 seconds before next call...'
					);
					await sleep(3000);

					return retryResponse;
				} catch (retryError: any) {
					console.error(
						` Retry failed with status: ${retryError.response?.status}`
					);
					console.error(` Retry error details:`, retryError.response?.data);
					throw retryError;
				}
			}

			// Handle 429 Rate Limiting errors with improved tracking and backoff
			if (error.response?.status === 429) {
				// Update rate limit status immediately when we hit 429
				updateRateLimitStatusFromError(error);

				const retryAfter = error.response.headers['retry-after'];
				const rateLimitProblem = error.response.headers['x-rate-limit-problem'];

				console.log(
					` Rate limit exceeded (429): ${rateLimitProblem || 'Unknown limit'}`
				);
				console.log(` Retry-After header: ${retryAfter || 'Not provided'}`);
				console.log(
					` Current retry attempt: ${retryAttempt}/${XERO_RATE_LIMITS.MAX_RETRIES}`
				);

				if (retryAttempt < XERO_RATE_LIMITS.MAX_RETRIES) {
					// Calculate delay: use Retry-After header if provided, otherwise exponential backoff
					let delayMs: number;
					if (retryAfter) {
						// Retry-After is in seconds, convert to milliseconds and add buffer
						delayMs = parseInt(retryAfter) * 1000 + 1000; // Add 1s buffer
					} else {
						// Use exponential backoff if no Retry-After header
						delayMs = calculateBackoffDelay(retryAttempt);
					}

					// Cap the delay to prevent extremely long waits
					const maxDelay = Math.min(
						delayMs,
						XERO_RATE_LIMITS.REQUEST_TIMEOUT / 2
					);

					console.log(
						` Waiting ${maxDelay}ms before retry attempt ${
							retryAttempt + 1
						}... (original delay: ${delayMs}ms)`
					);

					// Use timeout-aware sleep
					await Promise.race([
						sleep(maxDelay),
						new Promise((_, reject) =>
							setTimeout(
								() =>
									reject(
										new Error(`Rate limit wait timeout after ${maxDelay}ms`)
									),
								maxDelay + 1000
							)
						),
					]);

					// Clear rate limit status before retry since we've waited
					lastRateLimitStatus.isRateLimited = false;
					lastRateLimitStatus.rateLimitResetTime = 0;

					// Recursive retry with incremented attempt counter
					console.log(`Retrying API call after rate limit delay...`);
					return await xeroApiCall(
						method,
						endpoint,
						connection,
						data,
						retryAttempt + 1
					);
				} else {
					console.error(
						` Max retries (${XERO_RATE_LIMITS.MAX_RETRIES}) exceeded for rate limiting`
					);

					// Keep rate limit status active to prevent immediate subsequent calls
					lastRateLimitStatus.isRateLimited = true;
					lastRateLimitStatus.rateLimitResetTime =
						Date.now() + XERO_RATE_LIMITS.RATE_LIMIT_RESET_DELAY;

					throw new ApiException({
						status: 429,
						message: `Xero API rate limit exceeded. ${
							rateLimitProblem || 'Rate limit reached'
						}`,
						errorDescription: `Failed after ${XERO_RATE_LIMITS.MAX_RETRIES} retry attempts. Please try again later.`,
					});
				}
			}

			// Check if this is a Xero error and enhance the error object with structured information
			if (error.response?.data) {
				const errorData = error.response.data;

				// Handle validation errors (with ErrorNumber and Elements)
				if (errorData.ErrorNumber && errorData.Elements) {
					const validationErrors: string[] = [];

					errorData.Elements.forEach((element: any) => {
						if (
							element.ValidationErrors &&
							Array.isArray(element.ValidationErrors)
						) {
							element.ValidationErrors.forEach((validationError: any) => {
								if (validationError.Message) {
									validationErrors.push(validationError.Message);
								}
							});
						}
					});

					// Enhance the error object with structured validation error information
					error.xeroValidationErrors = {
						errorNumber: errorData.ErrorNumber,
						type: errorData.Type,
						message: errorData.Message,
						validationMessages: validationErrors,
						elements: errorData.Elements,
					};
				}
				// Handle unauthorized/authentication errors (with Title and Detail)
				else if (errorData.Title || errorData.Detail) {
					error.xeroValidationErrors = {
						type: errorData.Type || 'AuthenticationError',
						title: errorData.Title,
						status: errorData.Status,
						message:
							errorData.Detail || errorData.Title || 'Authentication error',
						validationMessages: [
							errorData.Detail || errorData.Title || 'Authentication error',
						],
						instance: errorData.Instance,
						extensions: errorData.Extensions,
					};
				}
				// Handle other Xero error formats
				else if (errorData.message || errorData.Message) {
					error.xeroValidationErrors = {
						type: 'XeroError',
						message: errorData.message || errorData.Message,
						validationMessages: [errorData.message || errorData.Message],
						fullErrorData: errorData,
					};
				}
			}

			// Log non-token errors for debugging
			console.error(' Xero API call failed:', {
				status: error.response?.status,
				data: error.response?.data,
				endpoint: endpoint,
				tenantId: tenantId,
				message: error.message,
				validationErrors: error.xeroValidationErrors?.validationMessages,
			});
			throw error;
		}
	}); // Close queueApiRequest
}

// Helper function to ensure we have a valid token
async function ensureValidXeroToken(connection: any): Promise<void> {
	// Initialize tokens if not already done
	await initializeTokens();

	const now = new Date();
	const credentials = connection.xeroCredentials as any;

	if (!credentials) {
		throw new ApiException({
			status: 500,
			message: 'No Xero credentials found',
			errorDescription:
				'Critical error: Xero credentials not found in connection object',
		});
	}

	const expiresAt = new Date(credentials.expiresAt);
	const expiresIn5Min = new Date(expiresAt.getTime() - 5 * 60 * 1000);

	// Refresh token if it's expired, close to expiry, or not loaded in memory
	if (!XERO_ACCESS_TOKEN || now >= expiresIn5Min) {
		await refreshXeroToken(connection);
	}
}

// Helper function to detect token expiry errors
function isTokenExpiredError(error: any): boolean {
	// Check for 401/403 status codes first
	if (!(error.response?.status === 401 || error.response?.status === 403)) {
		return false;
	}

	const responseData = error.response?.data;

	// Handle string response data
	if (typeof responseData === 'string') {
		return (
			responseData.includes('token_expired') ||
			responseData.includes('Unauthorized') ||
			responseData.includes('Invalid token') ||
			responseData.includes('AuthenticationUnsuccessful')
		);
	}

	// Handle JSON object response data (Xero's structured error format)
	if (typeof responseData === 'object' && responseData !== null) {
		const title = responseData.Title || '';
		const detail = responseData.Detail || '';
		const type = responseData.Type || '';

		return (
			title === 'Unauthorized' ||
			detail === 'AuthenticationUnsuccessful' ||
			detail.includes('token_expired') ||
			detail.includes('TokenExpired') || // Handle Xero's capitalized format
			detail.includes('Invalid token') ||
			type === 'AuthenticationException'
		);
	}

	// Default to true for 401/403 if we can't parse the response
	return true;
}

// 1. Pre-Operation Invoice Status Validation
async function validateXeroInvoiceStatus(
	existingXeroInvoice: any,
	connection: any,
	synclogId?: string
): Promise<{
	isEditable: boolean;
	status: string;
}> {
	const xeroInvoiceId = existingXeroInvoice.InvoiceID;

	if (!connection?.xeroCredentials) {
		throw new ApiException({
			status: 500,
			message: 'Missing Xero credentials for status validation',
			errorDescription:
				'Critical error: Xero credentials not found in connection object',
		});
	}

	try {
		console.log(
			`Validating Xero invoice status for invoice ID: ${existingXeroInvoice.xeroInvoiceId}`
		);

		if (synclogId) {
			await logSyncHistory(
				synclogId,
				'pending',
				`Validating Xero invoice status for invoice ${existingXeroInvoice.xeroInvoiceId}`
			);
		}

		const status = existingXeroInvoice.Status;
		const isEditable = !NON_EDITABLE_STATUSES.includes(status);

		console.log(
			`Invoice ${xeroInvoiceId} status: ${status}, editable: ${isEditable}`
		);

		if (synclogId) {
			await logSyncHistory(
				synclogId,
				'success',
				`Invoice ${xeroInvoiceId} status validated: ${status} (editable: ${isEditable})`
			);
		}

		return {
			isEditable,
			status,
		};
	} catch (error: any) {
		console.error(
			`Error validating Xero invoice status for ${xeroInvoiceId}:`,
			error.message
		);

		if (synclogId) {
			await logSyncHistory(
				synclogId,
				'error',
				`Failed to validate invoice status: ${error.message}`,
				{
					xeroInvoiceId,
					error: error.message,
				}
			);
		}

		// If we can't validate status, assume it's not editable and needs recreation
		return {
			isEditable: false,
			status: 'UNKNOWN',
		};
	}
}

// 2. Optimized Unified Void-and-Recreate Flow
async function handleVoidAndRecreateFlow(
	hsInvoice: IHubSpotInvoice,
	originalXeroInvoice: any,
	contactId: string,
	connection: any,
	synclogId?: string,
	reason: string = 'validation-error',
	hasPaymentsOrCredits?: boolean // Pre-checked payment/credit status
): Promise<void> {
	const originalInvoiceNumber = originalXeroInvoice.InvoiceNumber || 'Unknown';
	const originalXeroId = originalXeroInvoice.InvoiceID;

	try {
		console.log(
			`🔄 DEBUG: Starting void-and-recreate flow for invoice ${originalInvoiceNumber} (reason: ${reason})`
		);
		console.log(`🔄 DEBUG: Connection object exists: ${!!connection}`);
		console.log(`🔄 DEBUG: Original Xero ID: ${originalXeroId}`);
		console.log(
			`🔄 DEBUG: Has payments or credits pre-checked: ${hasPaymentsOrCredits}`
		);

		if (synclogId) {
			await logSyncHistory(
				synclogId,
				'pending',
				`Starting void-and-recreate flow for invoice ${originalInvoiceNumber} (reason: ${reason})`
			);
		}

		// Step 1: Streamlined void process with optimized cleanup
		if (originalXeroInvoice.Status === 'AUTHORISED') {
			try {
				console.log(
					` Starting optimized void process for invoice ${originalInvoiceNumber} (reason: ${reason})`
				);

				// Step 1a: Perform cleanup if we know payments/credits exist
				if (hasPaymentsOrCredits) {
					console.log(
						` Removing payments/credits from invoice ${originalInvoiceNumber} (pre-checked: true)`
					);

					if (synclogId) {
						await logSyncHistory(
							synclogId,
							'pending',
							`Removing payments/credits from invoice ${originalInvoiceNumber} (pre-checked)`
						);
					}

					try {
						// Direct cleanup - no redundant checking needed
						console.log(
							` Starting atomic cleanup for invoice ${originalInvoiceNumber}`
						);

						// Remove payments first
						console.log(
							`🔄 DEBUG: About to call removeInvoicePayments with connection: ${!!connection}`
						);
						await removeInvoicePayments(originalXeroId, connection);

						// Remove credit allocations
						await removeInvoiceCreditAllocations(originalXeroId, connection);

						// Verify cleanup was successful (final verification only)
						const stillHasPaymentsOrCredits =
							await checkInvoiceHasPaymentsOrCredits(
								originalXeroId,
								connection
							);

						if (stillHasPaymentsOrCredits) {
							throw new Error(
								`Cleanup verification failed - invoice ${originalInvoiceNumber} still has payments/credits after cleanup`
							);
						}

						console.log(
							` Successfully removed all payments/credits from invoice ${originalInvoiceNumber}`
						);

						if (synclogId) {
							await logSyncHistory(
								synclogId,
								'success',
								`Successfully removed all payments/credits from invoice ${originalInvoiceNumber}`
							);
						}
					} catch (cleanupError: any) {
						const cleanupErrorMessage = `Failed to cleanup payments/credits for invoice ${originalInvoiceNumber}: ${cleanupError.message}`;
						console.error(` ${cleanupErrorMessage}`);

						// Log detailed error information
						if (cleanupError.response?.data) {
							console.error(
								'Cleanup error details:',
								cleanupError.response.data
							);
						}

						if (synclogId) {
							await logSyncHistory(synclogId, 'error', cleanupErrorMessage, {
								originalXeroId,
								originalInvoiceNumber,
								error: cleanupError.message,
								errorDetails: cleanupError.response?.data,
							});
						}

						// If cleanup fails, abort the void operation to prevent partial state
						throw new Error(
							`Cannot void invoice ${originalInvoiceNumber} - payment/credit cleanup failed: ${cleanupError.message}`
						);
					}
				} else {
					console.log(
						` No payments/credits to remove for invoice ${originalInvoiceNumber} (pre-checked: false)`
					);
				}

				// Step 1b: Void the invoice after successful cleanup - let xeroApiCall handle token refresh
				console.log(` Voiding original invoice ${originalInvoiceNumber}`);

				try {
					await xeroApiCall('post', `/Invoices/${originalXeroId}`, connection, {
						Status: 'VOIDED',
					});

					console.log(
						` ✅ Successfully voided invoice ${originalInvoiceNumber}`
					);

					if (synclogId) {
						await logSyncHistory(
							synclogId,
							'success',
							`Successfully voided original invoice ${originalInvoiceNumber} after payment/credit cleanup`
						);
					}
				} catch (voidApiError: any) {
					const voidErrorMessage = `Failed to void invoice ${originalInvoiceNumber}: ${voidApiError.message}`;
					console.error(` ❌ ${voidErrorMessage}`);

					// Log detailed error information
					if (voidApiError.response?.data) {
						console.error(
							'Void operation error details:',
							voidApiError.response.data
						);
					}

					if (synclogId) {
						await logSyncHistory(synclogId, 'error', voidErrorMessage, {
							originalXeroId,
							originalInvoiceNumber,
							error: voidApiError.message,
							errorDetails: voidApiError.response?.data,
						});
					}

					throw new ApiException({
						status: voidApiError.response?.status || 500,
						message: voidErrorMessage,
						errorDescription: `Invoice voiding failed: ${
							voidApiError.message
						}. Error details: ${JSON.stringify(
							voidApiError.response?.data || {}
						)}`,
					});
				}
			} catch (voidError: any) {
				const voidProcessErrorMessage = `Void process failed for invoice ${originalInvoiceNumber}: ${voidError.message}`;
				console.warn(` ${voidProcessErrorMessage}`);

				if (synclogId) {
					await logSyncHistory(synclogId, 'error', voidProcessErrorMessage, {
						originalXeroId,
						originalInvoiceNumber,
						error: voidError.message,
						errorDetails: voidError.response?.data,
					});
				}

				// Continue with recreation even if voiding fails
				console.log(` Continuing with invoice recreation despite void failure`);
			}
		} else {
			console.log(
				`Skipping void for invoice ${originalInvoiceNumber} (Status: ${originalXeroInvoice.Status})`
			);

			if (synclogId) {
				await logSyncHistory(
					synclogId,
					'pending',
					`Skipping void for invoice ${originalInvoiceNumber} (Status: ${originalXeroInvoice.Status})`
				);
			}
		}

		// Step 2: Create replacement invoice
		console.log(
			` Creating new invoice in Xero with data from HubSpot invoice ${hsInvoice.id}`
		);

		if (synclogId) {
			await logSyncHistory(
				synclogId,
				'pending',
				`Creating new invoice in Xero with data from HubSpot invoice ${hsInvoice.id}`
			);
		}

		// Create new invoice without the original invoice number to avoid conflicts
		const hsInvoiceWithoutNumber = {
			...hsInvoice,
			properties: {
				...hsInvoice.properties,
				hs_invoice_number: undefined, // Let Xero generate new number
			},
		};

		let newInvoiceResult;
		try {
			newInvoiceResult = await createXeroInvoice(
				hsInvoiceWithoutNumber,
				contactId,
				connection,
				synclogId
			);
		} catch (createError: any) {
			const createErrorMessage = `Failed to create replacement invoice for ${originalInvoiceNumber}: ${createError.message}`;
			console.error(` ${createErrorMessage}`);

			// Log detailed error information
			if (createError.response?.data) {
				console.error(
					'Invoice creation error details:',
					createError.response.data
				);
			}

			if (synclogId) {
				await logSyncHistory(synclogId, 'error', createErrorMessage, {
					originalXeroId,
					originalInvoiceNumber,
					hsInvoiceId: hsInvoice.id,
					error: createError.message,
					errorDetails: createError.response?.data,
				});
			}

			throw new Error(`Invoice recreation failed: ${createError.message}`);
		}

		// Check if invoice was skipped due to no line items
		if (
			newInvoiceResult.Status === 400 &&
			newInvoiceResult.Message.includes('no line items')
		) {
			console.log(
				`Invoice ${hsInvoice.id} skipped during void-and-recreate due to no line items.`
			);

			await logSyncResult(
				hsInvoice.id,
				'error',
				`Skipped during void-and-recreate: ${newInvoiceResult.Message}`
			);

			if (synclogId) {
				await logSyncHistory(
					synclogId,
					'error',
					`Skipped during void-and-recreate: ${newInvoiceResult.Message}`
				);
			}

			return; // Skip this invoice
		}

		if (newInvoiceResult.InvoiceID && newInvoiceResult.Status === 200) {
			const newXeroId = newInvoiceResult.InvoiceID;

			try {
				// Get the new invoice details to get the new invoice number
				const newInvoiceDetails = await getXeroInvoiceById(
					newXeroId,
					connection
				);
				const newInvoiceNumber = newInvoiceDetails?.InvoiceNumber || newXeroId;

				console.log(
					` Created replacement invoice ${newInvoiceNumber} with Xero ID ${newXeroId}`
				);

				if (synclogId) {
					await logSyncHistory(
						synclogId,
						'success',
						`Created replacement invoice ${newInvoiceNumber} with Xero ID ${newXeroId}`
					);
				}

				// Step 3: Update database with new Xero invoice ID, preserving HubSpot associations
				console.log(` Updating database with new Xero invoice ID ${newXeroId}`);
				await updateSyncLogWithXeroId(
					hsInvoice.id,
					newXeroId,
					connection,
					hsInvoice
				);

				// Handle payment synchronization for the new invoice
				console.log(
					` Handling payment synchronization for new invoice ${newXeroId}`
				);
				await handleInvoicePaymentSync(
					newXeroId,
					hsInvoice,
					connection,
					synclogId
				);

				const successMessage = `Replaced invoice ${originalInvoiceNumber} with ${newInvoiceNumber} (${newXeroId})`;
				console.log(` ${successMessage}`);

				await logSyncResult(hsInvoice.id, 'success', successMessage);

				if (synclogId) {
					await logSyncHistory(
						synclogId,
						'success',
						`Void-and-recreate flow completed successfully: ${successMessage}`
					);
				}
			} catch (postCreateError: any) {
				const postCreateErrorMessage = `Post-creation steps failed for new invoice ${newXeroId}: ${postCreateError.message}`;
				console.error(` ${postCreateErrorMessage}`);

				if (synclogId) {
					await logSyncHistory(synclogId, 'error', postCreateErrorMessage, {
						originalXeroId,
						originalInvoiceNumber,
						newXeroId,
						hsInvoiceId: hsInvoice.id,
						error: postCreateError.message,
					});
				}

				// Even if post-creation steps fail, the invoice was created successfully
				await logSyncResult(
					hsInvoice.id,
					'error',
					`Invoice created but post-creation steps failed: ${postCreateErrorMessage}`
				);
			}
		} else {
			const failureMessage = `Failed to create replacement invoice: ${newInvoiceResult.Message}`;
			console.error(` ${failureMessage}`);

			if (synclogId) {
				await logSyncHistory(synclogId, 'error', failureMessage, {
					originalXeroId,
					originalInvoiceNumber,
					hsInvoiceId: hsInvoice.id,
					resultStatus: newInvoiceResult.Status,
					resultMessage: newInvoiceResult.Message,
				});
			}

			throw new Error(failureMessage);
		}
	} catch (error: any) {
		const errorMessage = `Failed to complete void-and-recreate for invoice ${originalInvoiceNumber}: ${error.message}`;
		console.error(errorMessage);

		if (synclogId) {
			await logSyncHistory(synclogId, 'error', errorMessage, {
				originalInvoiceNumber,
				originalXeroId,
				reason,
				error: error.message,
			});
		}

		await logSyncResult(hsInvoice.id, 'error', errorMessage);
		throw error;
	}
}

// Contact resolution with priority logic
async function resolveContact(
	invoice: IHubSpotInvoice,
	connection: any,
	synclogId?: string
): Promise<string> {
	const email = invoice.contactDetails?.email;
	const { hapikey } = connection?.hubSpotCredentials as any;

	if (synclogId) {
		await logSyncHistory(
			synclogId,
			'pending',
			`Starting contact resolution for invoice ${invoice.id} with email: ${
				email || 'no email'
			}`
		);
	}

	// Priority 1: Search by email
	if (email) {
		const emailResponse = await xeroApiCall(
			'get',
			`/Contacts?where=${encodeURIComponent(`EmailAddress=="${email}"`)}`,
			connection
		);

		if (
			emailResponse?.data?.Contacts &&
			Array.isArray(emailResponse.data.Contacts) &&
			emailResponse.data.Contacts.length > 0
		) {
			const contactId = emailResponse.data.Contacts[0].ContactID;
			console.log(`Found existing contact by email: ${email}`);

			if (synclogId) {
				await logSyncHistory(
					synclogId,
					'success',
					`Found existing contact for ${email} in Xero with #: ${contactId}`
				);
			}
			return contactId;
		} else {
			if (synclogId) {
				await logSyncHistory(
					synclogId,
					'error',
					`Contact not found for ${email} in Xero`
				);
			}
		}
	}

	// Get primary company ID using the dedicated function
	let primaryCompanyName = null;
	if (invoice.contactId) {
		const primaryCompanyId = await getPrimaryCompanyId(
			invoice.contactId,
			hapikey
		);
		if (primaryCompanyId) {
			const companyResponse = await axios.get(
				`https://api.hubapi.com/crm/v3/objects/companies/${primaryCompanyId}?properties=name`,
				{
					headers: {
						Authorization: `Bearer ${hapikey}`,
					},
				}
			);
			primaryCompanyName = companyResponse.data?.properties.name;
		}
	}

	// Priority 2: Search by company names
	const companyNames = [
		invoice?.properties?.deal_company_name,
		primaryCompanyName,
		invoice.contactDetails?.company,
	].filter(
		(name) => name && typeof name === 'string' && name.trim().length > 0
	);

	for (const companyName of companyNames) {
		const companyResponse = await xeroApiCall(
			'get',
			`/Contacts?where=${encodeURIComponent(`Name=="${companyName}"`)}`,
			connection
		);

		if (
			companyResponse?.data?.Contacts &&
			Array.isArray(companyResponse.data.Contacts) &&
			companyResponse.data.Contacts.length > 0
		) {
			console.log(`Found existing contact by company name: ${companyName}`);
			return companyResponse.data.Contacts[0].ContactID;
		}
	}

	// Priority 3: Search by full name
	const fullName = `${invoice.contactDetails?.firstname || ''} ${
		invoice.contactDetails?.lastname || ''
	}`.trim();
	if (fullName && fullName.length > 0) {
		const nameResponse = await xeroApiCall(
			'get',
			`/Contacts?where=${encodeURIComponent(`Name=="${fullName}"`)}`,
			connection
		);

		if (
			nameResponse?.data?.Contacts &&
			Array.isArray(nameResponse.data.Contacts) &&
			nameResponse.data.Contacts.length > 0
		) {
			console.log(`Found existing contact by full name: ${fullName}`);
			return nameResponse.data.Contacts[0].ContactID;
		}
	}

	// Create new contact with priority name logic
	// Priority: company name > firstname-lastname > email > fallback
	let contactName = '';

	if (companyNames.length > 0) {
		contactName = companyNames[0];
		console.log(`Creating contact with company name: ${contactName}`);
	} else if (fullName && fullName.length > 0) {
		contactName = fullName;
		console.log(`Creating contact with firstname-lastname: ${contactName}`);
	} else {
		return '';
	}

	const addresses = buildXeroAddress(invoice.properties);
	const newContactPayload = {
		Name: contactName,
		...(email && { EmailAddress: email }),
		...(invoice.contactDetails?.firstname && {
			FirstName: invoice.contactDetails.firstname,
		}),
		...(invoice.contactDetails?.lastname && {
			LastName: invoice.contactDetails.lastname,
		}),
		...(addresses && { Addresses: addresses }),
	};

	if (addresses) {
		const addr = addresses[0];
		console.log(`Adding POBOX address to Xero contact:`);
		console.log(`  AddressLine1: "${addr.AddressLine1}"`);
		console.log(`  AddressLine2: "${addr.AddressLine2}"`);
		console.log(`  City: "${addr.City}"`);
		console.log(`  Region: "${addr.Region}"`);
		console.log(`  PostalCode: "${addr.PostalCode}"`);
		console.log(`  Country: "${addr.Country}"`);
	}
	console.log('Creating new Xero contact with payload:', newContactPayload);

	if (synclogId) {
		await logSyncHistory(
			synclogId,
			'pending',
			`Adding contact for ${email || contactName} in Xero`
		);
	}

	try {
		const createResponse = await xeroApiCall('post', '/Contacts', connection, {
			Contacts: [newContactPayload],
		});

		if (
			createResponse?.data?.Contacts &&
			Array.isArray(createResponse.data.Contacts) &&
			createResponse.data.Contacts.length > 0
		) {
			const newContactId = createResponse.data.Contacts[0].ContactID;
			const successMessage = `Contact for ${
				email || contactName
			} created in Xero with #: ${newContactId}`;
			console.log(
				`Successfully created new Xero contact: ${newContactId} with name: ${contactName}`
			);

			if (synclogId) {
				await logSyncHistory(synclogId, 'success', successMessage);
			}
			return newContactId;
		} else {
			// If contact creation fails, log error and return empty string to continue processing
			const errorMessage = `Failed to create Xero contact for invoice ${invoice.id}: Invalid response structure`;
			console.error(errorMessage);

			if (synclogId) {
				await logSyncHistory(synclogId, 'error', errorMessage, {
					response: createResponse?.data || 'No response data',
					contactPayload: newContactPayload,
				});
			}
			return '';
		}
	} catch (error: any) {
		// Handle Xero errors (validation, authentication, etc.)
		let errorMessage = `Failed to create Xero contact for invoice ${invoice.id}`;

		if (error.xeroValidationErrors) {
			// Use the structured error information from xeroApiCall
			const validationMessages = error.xeroValidationErrors.validationMessages;

			// Handle different types of Xero errors
			if (error.xeroValidationErrors.type === 'AuthenticationError') {
				errorMessage = `Xero authentication error for invoice ${
					invoice.id
				}: ${validationMessages.join(', ')}`;
			} else {
				errorMessage =
					validationMessages.length > 0
						? `Xero contact validation error for invoice ${
								invoice.id
						  }: ${validationMessages.join(', ')}`
						: `Xero contact error for invoice ${invoice.id}: ${
								error.xeroValidationErrors.message || 'Unknown error'
						  }`;
			}

			if (synclogId) {
				await logSyncHistory(synclogId, 'error', errorMessage, {
					xeroValidationErrors: error.xeroValidationErrors,
					contactPayload: newContactPayload,
				});
			}
		} else {
			// Handle other types of errors (network, etc.)
			errorMessage = `Failed to create Xero contact for invoice ${invoice.id}: ${error.message}`;

			if (synclogId) {
				await logSyncHistory(synclogId, 'error', errorMessage, {
					error: error.message,
					httpStatus: error.response?.status,
					responseData: error.response?.data,
					contactPayload: newContactPayload,
				});
			}
		}

		console.error(errorMessage);
		return '';
	}
}

// Invoice payload construction is now handled directly in createXeroInvoice function

// Line item comparison is handled by compareInvoiceLineItems and compareDetailedLineItems functions

// Helper function to get Xero tenant ID from connection
function getXeroTenantId(connection: any): string {
	const credentials = connection?.xeroCredentials as any;
	return credentials?.tenantId || process.env.XERO_TENANT_ID || '';
}

// 1. Get invoices from HubSpot with all required properties
export async function getInvoicesFromHubSpot(
	connection: any,
	afterToken?: string,
	limit?: number,
	query?: {
		fromDate?: string;
		toDate?: string;
	}
): Promise<{
	invoices: IHubSpotInvoice[];
	paging?: { next?: { after?: string } };
}> {
	if (!connection?.hubSpotCredentials)
		throw new ApiException({
			status: 500,
			message: 'Missing HubSpot credentials',
			errorDescription:
				'Critical error: HubSpot credentials not found in connection object',
		});
	const { hapikey } = connection.hubSpotCredentials as any;

	// Build filter groups for date range if provided
	const filterGroups = [];
	if (query?.fromDate || query?.toDate) {
		const filters = [];

		if (query.fromDate) {
			// Convert date to ISO 8601 format with start of day (00:00:00Z)
			const fromDateISO = query.fromDate.includes('T')
				? query.fromDate
				: `${query.fromDate}T00:00:00Z`;
			filters.push({
				propertyName: 'hs_createdate',
				operator: 'GTE',
				value: fromDateISO,
			});
		}

		if (query.toDate) {
			// Convert date to ISO 8601 format with end of day (23:59:59Z)
			const toDateISO = query.toDate.includes('T')
				? query.toDate
				: `${query.toDate}T23:59:59Z`;
			filters.push({
				propertyName: 'hs_createdate',
				operator: 'LTE',
				value: toDateISO,
			});
		}

		filterGroups.push({ filters });
	}

	const requestBody = {
		limit: limit || 10,
		properties: [
			'amount',
			'status',
			'hs_number',
			'hs_invoice_number',
			'hs_discount_percentage',
			'hs_discount_total',
			'hs_quote_id',
			'hs_invoice_status',
			'quote',
			'hs_amount_billed',
			'hs_payment_status',
			'hs_payment_id',
			'hs_payment_ref_no',
			'hs_payment_notes',
			'hs_payment_amount',
			'hs_recipient_company_address',
			'hs_recipient_company_address2',
			'hs_recipient_company_city',
			'hs_recipient_company_state',
			'hs_recipient_company_country',
			'hs_recipient_company_zip',
			'hs_payment_date',
			'hs_currency',
			'hs_due_date',
		],
		associations: ['contacts', 'deals', 'quotes', 'line_items'],
		...(filterGroups.length > 0 && { filterGroups }),
		...(afterToken && { after: afterToken }),
	};

	console.log(
		'HubSpot API Request Body:',
		JSON.stringify(requestBody, null, 2)
	);

	const response = await axios.post(
		'https://api.hubapi.com/crm/v3/objects/invoices/search',
		requestBody,
		{
			headers: {
				Authorization: `Bearer ${hapikey}`,
				'Content-Type': 'application/json',
			},
		}
	);

	console.log(
		`HubSpot API Response: ${response.data.total || 0} invoices found`
	);

	// Transform invoices and fetch associations + contact details
	console.log(
		`Processing ${response.data.results?.length || 0} invoices from HubSpot...`
	);
	const transformedInvoices = await Promise.all(
		response.data.results?.map(async (invoice: any) => {
			console.log(`Processing invoice ${invoice.id}...`);
			// Use associations from the API response (since we requested them)
			const associations = invoice.associations || {};

			// Get contact IDs from associations
			const contactIds =
				associations?.contact?.results?.map((c: any) => c.id) || [];

			// Fetch contact details if we have contact IDs
			const contactDetailsMap =
				contactIds.length > 0
					? await fetchContactDetails(contactIds, hapikey)
					: {};

			const contactId = contactIds[0] || null;
			const contactDetails = contactId ? contactDetailsMap[contactId] : null;

			const dealId = associations?.deal?.results?.[0]?.id;
			const companyId = associations?.company?.results?.[0]?.id;
			const quoteId = associations?.quote?.results?.[0]?.id;

			// Build contact name from available data
			const contactName =
				contactDetails?.firstname && contactDetails?.lastname
					? `${contactDetails.firstname} ${contactDetails.lastname}`.trim()
					: contactDetails?.email?.split('@')[0] || 'Unknown Contact';

			// Get line items from associations if available, otherwise from properties
			const lineItemIds =
				associations?.line_item?.results?.map((li: any) => li.id) || [];
			let lineItems = [];

			if (lineItemIds.length > 0) {
				// TODO: Fetch line item details from HubSpot if needed
				// For now, fall back to properties
				lineItems = invoice?.properties.line_items
					? JSON.parse(invoice?.properties.line_items).map((item: any) => ({
							description: item.description,
							quantity: item.quantity || 1,
							unitPrice: item.unit_price || 0,
							taxCode: item.tax_code,
							itemCode: generateItemCode(item.hsProductId, item.description),
					  }))
					: [];
			} else {
				lineItems = invoice?.properties.line_items
					? JSON.parse(invoice?.properties.line_items).map((item: any) => ({
							description: item.description,
							quantity: item.quantity || 1,
							unitPrice: item.unit_price || 0,
							taxCode: item.tax_code,
							itemCode: generateItemCode(item.hsProductId, item.description),
					  }))
					: [];
			}

			return {
				id: invoice.id,
				amount: parseFloat(invoice?.properties.amount) || 0,
				currency: invoice?.properties?.hs_currency || 'USD',
				contactId: contactId,
				lineItems: lineItems,
				status: invoice?.properties.status || 'DRAFT',
				associations: associations,
				contactDetails: contactDetails,
				properties: {
					contact_email: contactDetails?.email,
					contact_firstname: contactDetails?.firstname,
					contact_lastname: contactDetails?.lastname,
					contact_name: contactName,
					contact_id: contactId,
					deal_id: dealId,
					company_id: companyId,
					quote_id: quoteId, // Add quote ID from associations
					...invoice?.properties,
				},
			};
		}) || []
	);

	return {
		invoices: transformedInvoices,
		paging: response.data.paging?.next
			? { next: { after: response.data.paging.next.after } }
			: undefined,
	};
}

// Helper function to get quote ID for an invoice
async function getQuoteIdForInvoice(
	invoiceId: string,
	connection: any
): Promise<string | null> {
	try {
		return await hubspotApi.getInvoiceQuoteAssociations(invoiceId, connection);
	} catch (error: any) {
		console.log(
			`Failed to get quote ID for invoice ${invoiceId}: ${error.message}`
		);
		return null;
	}
}

// Helper function to get deal ID for an invoice
async function getDealIdForInvoice(
	invoiceId: string,
	connection: any
): Promise<string | null> {
	try {
		return await hubspotApi.getInvoiceDealAssociations(invoiceId, connection);
	} catch (error: any) {
		console.log(
			`Failed to get deal ID for invoice ${invoiceId}: ${error.message}`
		);
		return null;
	}
}

// Helper function to fetch contact details from HubSpot
async function fetchContactDetails(contactIds: string[], hapikey: string) {
	if (!contactIds.length) return {};

	console.log(
		`Fetching details for ${contactIds.length} contacts from HubSpot...`
	);

	// Log each contact ID being fetched
	contactIds.forEach((id) => {
		console.log(`Fetching details for contact ID: ${id}`);
	});

	const response = await axios.post(
		'https://api.hubapi.com/crm/v3/objects/contacts/batch/read',
		{
			properties: [
				'email',
				'firstname',
				'lastname',
				'company',
				'phone',
				'city',
				'state',
				'country',
				'zip',
				'address',
			],
			inputs: contactIds.map((id) => ({ id })),
		},
		{
			headers: {
				Authorization: `Bearer ${hapikey}`,
				'Content-Type': 'application/json',
			},
		}
	);

	// Create a map of contactId -> contact details and log success for each
	const contactMap: any = {};
	response.data.results?.forEach((contact: any) => {
		console.log(
			` Successfully retrieved details for contact ID: ${contact.id}`
		);
		contactMap[contact.id] = contact?.properties;
	});

	console.log(
		`Successfully fetched ${response.data.results?.length || 0} contact details`
	);
	return contactMap;
}

// Helper function to fetch associations for an invoice
async function fetchInvoiceAssociations(invoiceId: string, hapikey: string) {
	const associationTypes = ['line_items', 'deals', 'contacts'];
	const associations: any = {};

	console.log(`  Fetching associations for invoice ${invoiceId}...`);

	await Promise.all(
		associationTypes.map(async (type) => {
			console.log(`🔗 Fetching ${type} associations for invoice ${invoiceId}`);
			const response = await axios.get(
				`https://api.hubapi.com/crm/v3/objects/invoice/${invoiceId}/associations/${type}`,
				{
					headers: {
						Authorization: `Bearer ${hapikey}`,
					},
				}
			);
			const resultCount = response.data.results?.length || 0;
			console.log(
				`Successfully retrieved ${resultCount} ${type} associations for invoice ${invoiceId}`
			);

			associations[type === 'line_items' ? 'line items' : type] = {
				results: response.data.results || [],
			};
		})
	);

	console.log(`Successfully fetched all associations for invoice ${invoiceId}`);
	return associations;
}

// 2. Enhanced function to check if invoice has payments or credits
async function checkInvoiceHasPaymentsOrCredits(
	invoiceId: string,
	connection: any,
	existingInvoice?: any
): Promise<boolean> {
	try {
		console.log(`Checking for payments/credits on invoice ${invoiceId}`);

		let invoice = existingInvoice;

		// Only fetch invoice if we don't already have it
		if (!invoice) {
			console.log(`Fetching invoice data from Xero API for ${invoiceId}`);
			const invoiceDetails = await xeroApiCall(
				'get',
				`/Invoices/${invoiceId}`,
				connection
			);
			if (!invoiceDetails || !invoiceDetails.data) {
				console.log(`Invoice details not found for invoice ${invoiceId}`);
				return false;
			}
			invoice = invoiceDetails.data?.Invoices?.[0];
		} else {
			console.log(
				`Using existing invoice data for ${invoiceId} (avoiding redundant API call)`
			);
		}

		if (!invoice) {
			console.log(`Invoice ${invoiceId} not found`);
			return false;
		}

		const hasPayments = (invoice.AmountPaid || 0) > 0;
		const hasCredits = (invoice.AmountCredited || 0) > 0;

		console.log(
			`Invoice ${invoiceId} - AmountPaid: ${
				invoice.AmountPaid || 0
			}, AmountCredited: ${invoice.AmountCredited || 0}`
		);

		return hasPayments || hasCredits;
	} catch (error: any) {
		console.error(
			`Error checking payments/credits for invoice ${invoiceId}:`,
			error.message
		);
		return false; // Assume no payments/credits if we can't check
	}
}

// 3. Remove payments from a Xero invoice
async function removeInvoicePayments(
	invoiceId: string,
	connection: any
): Promise<void> {
	try {
		console.log(` DEBUG: Removing payments from invoice ${invoiceId}`);
		console.log(
			` DEBUG: Connection object in removeInvoicePayments: ${!!connection}`
		);

		// First, get the invoice details to check for payments
		const invoiceResponse: any = await xeroApiCall(
			'get',
			`/Invoices/${invoiceId}`,
			connection
		);
		const invoice = invoiceResponse.data?.Invoices?.[0];

		if (!invoice) {
			console.log(`Invoice ${invoiceId} not found`);
			return;
		}

		// Check if invoice has any payments
		const amountPaid = parseFloat(invoice.AmountPaid || '0');
		if (amountPaid <= 0) {
			console.log(
				`No payments found for invoice ${invoiceId} (AmountPaid: ${amountPaid})`
			);
			return;
		}

		console.log(
			`Invoice ${invoiceId} has payments totaling ${amountPaid}, fetching payment details...`
		);

		// Get payments for this invoice
		const paymentsResponse: any = await xeroApiCall(
			'get',
			`/Payments?where=Invoice.InvoiceID%3DGuid%28%22${invoiceId}%22%29`,
			connection
		);
		const payments = paymentsResponse.data?.Payments || [];

		if (payments.length === 0) {
			console.log(
				`No payment records found for invoice ${invoiceId} despite AmountPaid > 0`
			);
			return;
		}

		console.log(
			`Found ${payments.length} payment(s) to remove from invoice ${invoiceId}`
		);

		// Delete each payment - let xeroApiCall handle token refresh automatically
		for (const payment of payments) {
			try {
				console.log(
					`Deleting payment ${payment.PaymentID} (Amount: ${payment.Amount}) from invoice ${invoiceId}`
				);

				await xeroApiCall(
					'post',
					`/Payments/${payment.PaymentID}`,
					connection,
					{ Status: 'DELETED' }
				);

				console.log(
					` Successfully deleted payment ${payment.PaymentID} from invoice ${invoiceId}`
				);
			} catch (paymentError: any) {
				console.error(
					` Failed to delete payment ${payment.PaymentID}:`,
					paymentError.message
				);

				// Log detailed error information
				if (paymentError.response?.data) {
					console.error(
						'Payment deletion error details:',
						paymentError.response.data
					);
				}

				// Throw ApiException with proper error details
				throw new ApiException({
					status: paymentError.response?.status || 500,
					message: `Failed to delete payment ${payment.PaymentID}`,
					errorDescription: `Payment deletion failed: ${
						paymentError.message
					}. Error details: ${JSON.stringify(
						paymentError.response?.data || {}
					)}`,
				});
			}
		}

		console.log(
			` Successfully removed all ${payments.length} payment(s) from invoice ${invoiceId}`
		);
	} catch (error: any) {
		console.error(
			` Error removing payments from invoice ${invoiceId}:`,
			error.message
		);

		// Log detailed error information for debugging
		if (error.response?.data) {
			console.error('Detailed error response:', error.response.data);
		}

		// If it's already an ApiException, re-throw it
		if (error.status && error.message && error.errorDescription) {
			throw error;
		}

		// Otherwise, wrap it in an ApiException
		throw new ApiException({
			status: 500,
			message: `Payment removal failed for invoice ${invoiceId}`,
			errorDescription: `Critical error during payment removal: ${
				error.message
			}. Error details: ${JSON.stringify(error.response?.data || {})}`,
		});
	}
}

// 4. Remove credit allocations from a Xero invoice
async function removeInvoiceCreditAllocations(
	invoiceId: string,
	connection: any
): Promise<void> {
	try {
		console.log(`Removing credit allocations from invoice ${invoiceId}`);

		// First, get the invoice details to check for credit allocations
		const invoiceResponse: any = await xeroApiCall(
			'get',
			`/Invoices/${invoiceId}`,
			connection
		);
		const invoice = invoiceResponse.data?.Invoices?.[0];

		if (!invoice) {
			console.log(`Invoice ${invoiceId} not found`);
			return;
		}

		// Check if invoice has any credits applied
		const amountCredited = parseFloat(invoice.AmountCredited || '0');
		if (amountCredited <= 0) {
			console.log(
				`No credit allocations found for invoice ${invoiceId} (AmountCredited: ${amountCredited})`
			);
			return;
		}

		console.log(
			`Invoice ${invoiceId} has credits totaling ${amountCredited}, fetching credit allocation details...`
		);

		// Get credit notes that have allocations to this invoice
		const creditNotesResponse: any = await xeroApiCall(
			'get',
			`/CreditNotes?where=Status%3D%22AUTHORISED%22`,
			connection
		);
		const allCreditNotes = creditNotesResponse.data?.CreditNotes || [];

		// Filter credit notes that have allocations to our invoice
		const relevantCreditNotes = [];
		for (const creditNote of allCreditNotes) {
			if (creditNote.Allocations && creditNote.Allocations.length > 0) {
				const hasAllocationToInvoice = creditNote.Allocations.some(
					(allocation: any) => allocation.Invoice?.InvoiceID === invoiceId
				);
				if (hasAllocationToInvoice) {
					relevantCreditNotes.push(creditNote);
				}
			}
		}

		if (relevantCreditNotes.length === 0) {
			console.log(
				`No credit notes with allocations found for invoice ${invoiceId} despite AmountCredited > 0`
			);
			return;
		}

		console.log(
			`Found ${relevantCreditNotes.length} credit note(s) with allocations to invoice ${invoiceId}`
		);

		// Remove allocations from each relevant credit note
		for (const creditNote of relevantCreditNotes) {
			const allocationsToRemove = creditNote.Allocations.filter(
				(allocation: any) => allocation.Invoice?.InvoiceID === invoiceId
			);

			for (const allocation of allocationsToRemove) {
				try {
					console.log(
						`Removing credit allocation ${allocation.AllocationID} (Amount: ${allocation.Amount}) from invoice ${invoiceId}`
					);

					await xeroApiCall(
						'delete',
						`/CreditNotes/${creditNote.CreditNoteID}/Allocations/${allocation.AllocationID}`,
						connection
					);

					console.log(
						` ✅ Successfully removed credit allocation ${allocation.AllocationID} from invoice ${invoiceId}`
					);
				} catch (allocationError: any) {
					console.error(
						` ❌ Failed to remove credit allocation ${allocation.AllocationID}:`,
						allocationError.message
					);

					// Log detailed error information
					if (allocationError.response?.data) {
						console.error(
							'Credit allocation removal error details:',
							allocationError.response.data
						);
					}

					// Throw ApiException with proper error details
					throw new ApiException({
						status: allocationError.response?.status || 500,
						message: `Failed to remove credit allocation ${allocation.AllocationID}`,
						errorDescription: `Credit allocation removal failed: ${
							allocationError.message
						}. Error details: ${JSON.stringify(
							allocationError.response?.data || {}
						)}`,
					});
				}
			}
		}

		console.log(
			` Successfully removed all credit allocations from invoice ${invoiceId}`
		);
	} catch (error: any) {
		console.error(
			` Error removing credit allocations from invoice ${invoiceId}:`,
			error.message
		);

		// Log detailed error information for debugging
		if (error.response?.data) {
			console.error('Detailed error response:', error.response.data);
		}

		// If it's already an ApiException, re-throw it
		if (error.status && error.message && error.errorDescription) {
			throw error;
		}

		// Otherwise, wrap it in an ApiException
		throw new ApiException({
			status: 500,
			message: `Credit allocation removal failed for invoice ${invoiceId}`,
			errorDescription: `Critical error during credit allocation removal: ${
				error.message
			}. Error details: ${JSON.stringify(error.response?.data || {})}`,
		});
	}
}

// 5. Enhanced delete payments function with full cleanup
export async function deleteXeroPayments(
	invoiceId: string,
	connection?: any
): Promise<void> {
	console.log(`🚨 DEBUG: deleteXeroPayments called for invoice ${invoiceId}`);
	console.log(`🚨 DEBUG: Connection provided: ${!!connection}`);

	if (!connection) {
		console.warn(
			`🚨 DEBUG: Manual deletion required for payments on invoice ${invoiceId} - no connection provided`
		);
		return;
	}

	try {
		console.log(`Starting payment/credit cleanup for invoice ${invoiceId}`);

		// Check if invoice has payments or credits
		const hasPaymentsOrCredits = await checkInvoiceHasPaymentsOrCredits(
			invoiceId,
			connection
		);

		if (!hasPaymentsOrCredits) {
			console.log(
				`No payments or credits found for invoice ${invoiceId}, cleanup not needed`
			);
			return;
		}

		// Remove payments first
		await removeInvoicePayments(invoiceId, connection);

		// // Remove credit allocations
		await removeInvoiceCreditAllocations(invoiceId, connection);

		console.log(
			` Successfully completed payment/credit cleanup for invoice ${invoiceId}`
		);
	} catch (error: any) {
		console.error(
			` Failed to complete payment/credit cleanup for invoice ${invoiceId}:`,
			error.message
		);
		throw error;
	}
}

// 3. Void a Xero invoice
export async function voidXeroInvoice(
	invoiceId: string,
	connection: any
): Promise<void> {
	try {
		console.log(`Voiding Xero invoice ${invoiceId}`);

		await xeroApiCall('post', `/Invoices/${invoiceId}`, connection, {
			Invoices: [
				{
					Status: 'VOIDED',
				},
			],
		});

		console.log(` Successfully voided Xero invoice ${invoiceId}`);
	} catch (error: any) {
		console.error(` Failed to void Xero invoice ${invoiceId}:`, error.message);

		// Log detailed error information for debugging
		if (error.response?.data) {
			console.error('Void operation error details:', error.response.data);
		}

		throw new Error(`Failed to void invoice ${invoiceId}: ${error.message}`);
	}
}

// 4. Create invoice in Xero with enhanced currency handling
export async function createXeroInvoice(
	hs: IHubSpotInvoice,
	contactId: string,
	connection: any,
	synclogId?: string
): Promise<{ InvoiceID: string; Message: string; Status: number }> {
	if (synclogId) {
		await logSyncHistory(
			synclogId,
			'pending',
			`Starting invoice creation in Xero for HubSpot invoice #: ${hs.id}`
		);
	}

	if (!connection?.xeroCredentials) {
		const errorMessage = 'Missing Xero credentials';
		if (synclogId) {
			await logSyncHistory(synclogId, 'error', errorMessage, {
				error: 'Xero credentials not found in connection object',
			});
		}
		throw new ApiException({
			status: 500,
			message: errorMessage,
			errorDescription:
				'Critical error: Xero credentials not found in connection object',
		});
	}
	// Handle multi-currency invoices
	let currencyCode = hs.currency || 'USD';
	// let convertedAmount = hs.amount;

	// Ensure invoice currency in HubSpot matches Xero
	const supportedCurrencies = ['USD', 'EUR', 'GBP', 'AUD', 'CAD', 'NZD', 'JPY'];
	if (!supportedCurrencies.includes(currencyCode)) {
		console.warn(
			`Currency ${currencyCode} not supported by Xero, defaulting to USD`
		);
		// Log discrepancies if currency unsupported by Xero
		await logSyncResult(
			hs.id,
			'error',
			`Unsupported currency ${currencyCode}, using USD`
		);
		currencyCode = 'USD';
		// In a real implementation, you would convert currency using exchange rate on invoice date
		// convertedAmount = await convertCurrency(hs.amount, hs.currency, 'USD', invoiceDate);
	}

	// Get line items from database instead of relying on HubSpot object
	let lineItems: any[] = [];

	try {
		// Fetch the invoice with line items from database
		const dbInvoice = await prisma.invoices.findFirst({
			where: { hubSpotInvoiceId: hs.id },
			include: { InvoiceLines: true },
		});

		if (dbInvoice?.InvoiceLines && dbInvoice.InvoiceLines.length > 0) {
			console.log(
				`Found ${dbInvoice.InvoiceLines.length} line items in database for invoice ${hs.id}`
			);

			// Use line items from database
			lineItems = dbInvoice.InvoiceLines.map((item) => ({
				Description: item.description || item.productName || 'Service',
				ItemCode:
					item.sku ||
					generateItemCode(
						item.hubSpotProductId,
						item.description || item.productName
					),
				Quantity: item.quantity || 1,
				UnitAmount: item.unitPrice || 0,
				AccountCode: XERO_ACCOUNT_CODE,
				TaxType: XERO_TAXTYPE_CODE,
			}));

			// Log line items for debugging
			console.log(`Line items for Xero invoice ${hs.id}:`);
			lineItems.forEach((item, index) => {
				console.log(
					`  ${index + 1}. ${item.Description} - Qty: ${
						item.Quantity
					}, Price: ${item.UnitAmount}`
				);
			});
		} else {
			console.log(
				`No line items found in database for invoice ${hs.id}, checking HubSpot object...`
			);

			// Fallback to HubSpot line items if database doesn't have them
			if (hs.lineItems && hs.lineItems.length > 0) {
				console.log(
					`Using ${hs.lineItems.length} line items from HubSpot object for invoice ${hs.id}`
				);
				lineItems = hs.lineItems.map((item) => ({
					Description: item.description || 'Service',
					ItemCode: generateItemCode(item.hsProductId, item.description),
					Quantity: item.quantity || 1,
					UnitAmount: item.unitPrice || 0,
					AccountCode: XERO_ACCOUNT_CODE,
					TaxType: XERO_TAXTYPE_CODE,
				}));

				// Log line items for debugging
				console.log(`HubSpot line items for Xero invoice ${hs.id}:`);
				lineItems.forEach((item, index) => {
					console.log(
						`  ${index + 1}. ${item.Description}
						} - Qty: ${item.Quantity}, Price: ${item.UnitAmount}`
					);
				});
			} else {
				// No line items found anywhere - skip this invoice
				const errorMessage = `Invoice ${hs.id} has no line items. Skipping Xero creation as Xero requires at least one line item.`;
				console.log(errorMessage);

				if (synclogId) {
					await logSyncHistory(synclogId, 'error', errorMessage, {
						reason: 'No line items found',
						hubSpotInvoiceId: hs.id,
						invoiceAmount: hs.amount || 0,
						skipReason: 'Xero requires at least one line item',
					});
				}

				return {
					InvoiceID: '',
					Message: errorMessage,
					Status: 400,
				};
			}
		}
	} catch (error: any) {
		console.error(
			`Error fetching line items from database for invoice ${hs.id}:`,
			error.message
		);

		// Fallback to HubSpot line items or skip invoice
		if (hs.lineItems && hs.lineItems.length > 0) {
			lineItems = hs.lineItems.map((item) => ({
				Description: item.description || 'Service',
				ItemCode: generateItemCode(item.hsProductId, item.description),
				Quantity: item.quantity || 1,
				UnitAmount: item.unitPrice || 0,
				AccountCode: XERO_ACCOUNT_CODE,
				TaxType: XERO_TAXTYPE_CODE,
			}));
		} else {
			// No line items found anywhere - skip this invoice
			const errorMessage = `Invoice ${hs.id} has no line items (database error occurred). Skipping Xero creation as Xero requires at least one line item.`;
			console.log(errorMessage);

			if (synclogId) {
				await logSyncHistory(synclogId, 'error', errorMessage, {
					reason: 'No line items found after database error',
					hubSpotInvoiceId: hs.id,
					invoiceAmount: hs.amount || 0,
					skipReason: 'Xero requires at least one line item',
					originalError: error.message,
				});
			}

			return {
				InvoiceID: '',
				Message: errorMessage,
				Status: 400,
			};
		}
	}

	// 1. Extract HubSpot Creation Date with proper fallback logic
	let invoiceDate: string;
	const hubSpotCreateDate = hs?.properties?.hs_createdate;

	if (hubSpotCreateDate) {
		// Convert HubSpot creation date format to Xero format (YYYY-MM-DD)
		try {
			const createDateObj = new Date(hubSpotCreateDate);
			if (!isNaN(createDateObj.getTime())) {
				invoiceDate = createDateObj.toISOString().split('T')[0];
				console.log(
					`Using HubSpot creation date: ${hubSpotCreateDate} -> ${invoiceDate}`
				);
			} else {
				throw new Error('Invalid date format');
			}
		} catch (error) {
			console.warn(
				`Invalid HubSpot creation date format: ${hubSpotCreateDate}, using fallback`
			);
			// First fallback: issue_date
			const issueDate = hs?.properties?.issue_date;
			if (issueDate) {
				try {
					const issueDateObj = new Date(issueDate);
					if (!isNaN(issueDateObj.getTime())) {
						invoiceDate = issueDateObj.toISOString().split('T')[0];
						console.log(
							`Using HubSpot issue date fallback: ${issueDate} -> ${invoiceDate}`
						);
					} else {
						throw new Error('Invalid issue date format');
					}
				} catch (issueError) {
					console.warn(
						`Invalid HubSpot issue date format: ${issueDate}, using current date`
					);
					invoiceDate = new Date().toISOString().split('T')[0];
					console.log(`Using current date fallback: ${invoiceDate}`);
				}
			} else {
				// Final fallback: current date
				invoiceDate = new Date().toISOString().split('T')[0];
				console.log(
					`No issue date found, using current date fallback: ${invoiceDate}`
				);
			}
		}
	} else {
		// First fallback: issue_date
		const issueDate = hs?.properties?.issue_date;
		if (issueDate) {
			try {
				const issueDateObj = new Date(issueDate);
				if (!isNaN(issueDateObj.getTime())) {
					invoiceDate = issueDateObj.toISOString().split('T')[0];
					console.log(
						`No HubSpot creation date, using issue date: ${issueDate} -> ${invoiceDate}`
					);
				} else {
					throw new Error('Invalid issue date format');
				}
			} catch (error) {
				console.warn(
					`Invalid HubSpot issue date format: ${issueDate}, using current date`
				);
				invoiceDate = new Date().toISOString().split('T')[0];
				console.log(`Using current date fallback: ${invoiceDate}`);
			}
		} else {
			// Final fallback: current date
			invoiceDate = new Date().toISOString().split('T')[0];
			console.log(
				`No HubSpot creation date or issue date found, using current date: ${invoiceDate}`
			);
		}
	}

	// 2. Date Format Handling - Extract due date from HubSpot
	let dueDate: string;
	const hubSpotDueDate = hs?.properties?.hs_due_date;

	if (hubSpotDueDate) {
		// Convert HubSpot date format to Xero format (YYYY-MM-DD)
		try {
			const dueDateObj = new Date(hubSpotDueDate);
			if (!isNaN(dueDateObj.getTime())) {
				dueDate = dueDateObj.toISOString().split('T')[0];
				console.log(`Using HubSpot due date: ${hubSpotDueDate} -> ${dueDate}`);
			} else {
				throw new Error('Invalid date format');
			}
		} catch (error) {
			console.warn(
				`Invalid HubSpot due date format: ${hubSpotDueDate}, using fallback`
			);
			// 3. Fallback Logic - Invoice date + 30 days
			const fallbackDate = new Date(invoiceDate);
			fallbackDate.setDate(fallbackDate.getDate() + 30);
			dueDate = fallbackDate.toISOString().split('T')[0];
			console.log(
				`Using fallback due date (invoice date + 30 days): ${dueDate}`
			);
		}
	} else {
		// 4. Fallback Logic - If no due date, use invoice date + 30 days
		const fallbackDate = new Date(invoiceDate);
		fallbackDate.setDate(fallbackDate.getDate() + 30);
		dueDate = fallbackDate.toISOString().split('T')[0];
		console.log(
			`No HubSpot due date found, using fallback (invoice date + 30 days): ${dueDate}`
		);
	}

	// 5. Logging - Show due date being used
	console.log(
		`Setting Xero invoice due date: ${dueDate} for HubSpot invoice ${hs.id}`
	);

	// Map HubSpot status to Xero status
	function mapHubSpotStatusToXero(hsStatus: string): string {
		const statusMap: { [key: string]: string } = {
			DRAFT: 'DRAFT',
			PENDING: 'DRAFT',
			SENT: 'SUBMITTED',
			PAID: 'AUTHORISED', // Will be marked as paid later via payment sync
			OVERDUE: 'AUTHORISED',
			CANCELLED: 'DRAFT', // Create as draft, can be voided later if needed
			VOID: 'DRAFT', // Create as draft, can be voided later if needed
		};

		return statusMap[hsStatus?.toUpperCase()] || 'DRAFT'; // Default to DRAFT for safety
	}

	const hsStatus =
		hs?.properties?.hs_invoice_status || hs?.properties?.status || 'DRAFT';
	const xeroStatus = mapHubSpotStatusToXero(hsStatus);

	console.log(
		`Mapping HubSpot status '${hsStatus}' to Xero status '${xeroStatus}' for invoice ${hs.id}`
	);

	const payload = {
		Type: 'ACCREC',
		Contact: { ContactID: contactId },
		LineItems: lineItems,
		Reference: hs.id,
		Date: invoiceDate,
		DueDate: dueDate,
		Status: xeroStatus,
		CurrencyCode: currencyCode,
		InvoiceNumber: hs?.properties?.invoice_number || undefined,
	};

	try {
		// Log the payload being sent to Xero for debugging
		console.log(`Sending invoice payload to Xero for invoice ${hs.id}:`);
		console.log(`- Contact ID: ${contactId}`);
		console.log(`- Line Items Count: ${lineItems.length}`);
		console.log(`- Currency: ${currencyCode}`);
		console.log(`- Invoice Date: ${invoiceDate}`);
		console.log(`- Due Date: ${dueDate}`);
		console.log(
			`- HubSpot Creation Date Source: ${hubSpotCreateDate || 'Not provided'}`
		);
		console.log(
			`- HubSpot Due Date Source: ${hubSpotDueDate || 'Not provided'}`
		);
		console.log(`- Total Line Items:`, JSON.stringify(lineItems, null, 2));

		// Wrap the payload in Invoices array as required by Xero API
		const xeroPayload = {
			Invoices: [payload],
		};

		const response = await xeroApiCall(
			'post',
			'/Invoices',
			connection,
			xeroPayload
		);
		//  axios.post(
		// 	`${XERO_API_BASE}/Invoices`,
		// 	{ Invoices: [payload] },
		// 	{
		// 		headers: {
		// 			Authorization: `Bearer ${access_token}`,
		// 			'Xero-tenant-id': getXeroTenantId(connection),
		// 			Accept: 'application/json',
		// 			'Content-Type': 'application/json',
		// 		},
		// 	}
		// );

		// Check if response contains validation errors
		if (response?.data.ErrorNumber) {
			// If the Xero API has an error (e.g., ValidationException)
			const errorMessage = response.data.Elements.map((element: any) =>
				element.ValidationErrors
					? element.ValidationErrors.map((err: any) => err.Message).join(', ')
					: ''
			).join(', ');

			if (synclogId) {
				await logSyncHistory(
					synclogId,
					'error',
					`Validation error while creating invoice in Xero: ${errorMessage}`,
					{
						Type: 'ValidationException',
						Message: 'A validation exception occurred',
						Elements: response.data.Elements,
						ErrorNumber: response.data.ErrorNumber,
					}
				);
			}

			return {
				InvoiceID: '',
				Message: errorMessage || response.data.Message || 'Unknown error',
				Status: 400,
			};
		}

		// If invoice creation was successful
		if (response?.data.Invoices && response?.data.Invoices.length > 0) {
			const created = response?.data.Invoices[0];

			// Update database with Xero invoice details
			await prisma.invoices.updateMany({
				where: { hubSpotInvoiceId: hs.id },
				data: {
					xeroInvoiceId: created.InvoiceID,
					xeroInvoiceNumber: created.InvoiceNumber,
				},
			});

			const successMessage = `Invoice created in Xero with #: ${created.InvoiceID} for HubSpot invoice #: ${hs.id}`;
			console.log(
				`Created Xero invoice ${created.InvoiceID} for HubSpot invoice ${hs.id}`
			);

			if (synclogId) {
				await logSyncHistory(synclogId, 'success', successMessage);
			}

			// Return success response
			return {
				InvoiceID: created.InvoiceID,
				Message: 'Successfully created',
				Status: 200,
			};
		}

		// If no invoices are created
		return {
			InvoiceID: '',
			Message: 'No invoices created',
			Status: 400,
		};
	} catch (error: any) {
		try {
			// Add debugging to see the actual error structure
			console.log(
				'Full error object:',
				JSON.stringify(
					{
						message: error.message,
						status: error.response?.status,
						data: error.response?.data,
						headers: error.response?.headers,
					},
					null,
					2
				)
			);

			// Handle HTTP errors (400, 401, 403, etc.) that contain Xero validation errors
			if (error.response?.data) {
				const errorData = error.response.data;

				// Log the error data structure for debugging
				console.log(
					'Error data structure:',
					JSON.stringify(errorData, null, 2)
				);

				// Check if this is a Xero validation error response (multiple possible structures)
				if (
					(errorData.ErrorNumber && errorData.Elements) ||
					errorData.ValidationErrors ||
					errorData.Elements ||
					errorData.Type === 'ValidationException'
				) {
					const validationErrors: string[] = [];
					const detailedErrors: any[] = [];

					// Handle direct ValidationErrors at root level
					if (errorData.ValidationErrors) {
						const validationErrorsArray = Array.isArray(
							errorData.ValidationErrors
						)
							? errorData.ValidationErrors
							: [errorData.ValidationErrors];

						validationErrorsArray.forEach((validationError: any) => {
							if (validationError?.Message) {
								validationErrors.push(validationError.Message);
							} else if (typeof validationError === 'string') {
								validationErrors.push(validationError);
							}
						});
					}

					// Handle Elements array
					if (errorData.Elements && Array.isArray(errorData.Elements)) {
						errorData.Elements.forEach((element: any) => {
							try {
								// Handle elements with validation errors
								if (element?.ValidationErrors) {
									const validationErrorsArray = Array.isArray(
										element.ValidationErrors
									)
										? element.ValidationErrors
										: [element.ValidationErrors];

									validationErrorsArray.forEach((validationError: any) => {
										if (validationError?.Message) {
											validationErrors.push(validationError.Message);
										} else if (typeof validationError === 'string') {
											validationErrors.push(validationError);
										}
									});

									detailedErrors.push({
										Type: element.Type || 'Unknown',
										InvoiceID: element.InvoiceID || null,
										Reference: element.Reference || null,
										ValidationErrors: element.ValidationErrors,
									});
								}
								// Handle elements that might have other error indicators
								else if (element?.Message) {
									validationErrors.push(element.Message);
									detailedErrors.push({
										Type: element.Type || 'Unknown',
										InvoiceID: element.InvoiceID || null,
										Reference: element.Reference || null,
										Message: element.Message,
									});
								}
								// Handle elements that are just error strings
								else if (typeof element === 'string') {
									validationErrors.push(element);
								}
							} catch (elementError) {
								console.warn(
									'Error processing validation element:',
									elementError
								);
							}
						});
					}

					// Construct error message from various possible sources
					const errorMessage =
						validationErrors.length > 0
							? validationErrors.join(', ')
							: errorData.Message ||
							  errorData.Detail ||
							  errorData.message ||
							  errorData.error ||
							  'Validation error occurred';

					console.log('Constructed validation error message:', errorMessage);

					if (synclogId) {
						try {
							await logSyncHistory(
								synclogId,
								'error',
								`Xero API validation error: ${errorMessage}`,
								{
									Type: errorData.Type || 'ValidationException',
									Message:
										errorData.Message || 'A validation exception occurred',
									ErrorNumber: errorData.ErrorNumber,
									Elements:
										detailedErrors.length > 0
											? detailedErrors
											: errorData.Elements,
									httpStatus: error.response?.status,
									fullErrorResponse: errorData,
								}
							);
						} catch (logError) {
							console.error('Failed to log sync history:', logError);
						}
					}

					return {
						InvoiceID: '',
						Message: errorMessage,
						Status: error.response?.status || 400,
					};
				}

				// Handle unauthorized/authentication errors (with Title and Detail)
				else if (errorData.Title || errorData.Detail) {
					const errorMessage =
						errorData.Detail || errorData.Title || 'Authentication error';

					console.log('Handling authentication error:', errorMessage);

					if (synclogId) {
						try {
							await logSyncHistory(
								synclogId,
								'error',
								`Xero authentication error: ${errorMessage}`,
								{
									Type: errorData.Type || 'AuthenticationError',
									Title: errorData.Title,
									Status: errorData.Status,
									Detail: errorData.Detail,
									Instance: errorData.Instance,
									Extensions: errorData.Extensions,
									httpStatus: error.response?.status,
									fullErrorResponse: errorData,
								}
							);
						} catch (logError) {
							console.error('Failed to log sync history:', logError);
						}
					}

					return {
						InvoiceID: '',
						Message: errorMessage,
						Status: error.response?.status || 401,
					};
				}
				// Handle any other structured response from Xero
				else if (typeof errorData === 'object') {
					const errorMessage =
						errorData.Message ||
						errorData.message ||
						errorData.error ||
						errorData.title ||
						'API error occurred';

					console.log('Handling structured API error:', errorMessage);

					if (synclogId) {
						try {
							await logSyncHistory(
								synclogId,
								'error',
								`Xero API error: ${errorMessage}`,
								{
									Type: errorData.Type || 'APIException',
									Message: errorMessage,
									ErrorNumber: errorData.ErrorNumber || null,
									httpStatus: error.response?.status,
									fullErrorResponse: errorData,
								}
							);
						} catch (logError) {
							console.error('Failed to log sync history:', logError);
						}
					}

					return {
						InvoiceID: '',
						Message: errorMessage,
						Status: error.response?.status || 400,
					};
				}
			}

			// Handle other types of errors (network, timeout, etc.)
			const errorMessage =
				error.response?.data?.message ||
				error.response?.statusText ||
				error.message ||
				'Unknown error occurred';

			console.log('Handling generic error:', errorMessage);

			if (synclogId) {
				try {
					await logSyncHistory(
						synclogId,
						'error',
						`Error creating invoice in Xero: ${errorMessage}`,
						{
							error: errorMessage,
							httpStatus: error.response?.status,
							responseData: error.response?.data,
							stack: error.stack,
							errorType: error.name || 'UnknownError',
						}
					);
				} catch (logError) {
					console.error('Failed to log sync history:', logError);
				}
			}

			return {
				InvoiceID: '',
				Message: errorMessage,
				Status: error.response?.status || 500,
			};
		} catch (error: any) {
			console.error('Critical error in error handling:', error);

			const fallbackMessage =
				'Critical error occurred while processing Xero response';

			if (synclogId) {
				try {
					await logSyncHistory(synclogId, 'error', fallbackMessage, {
						originalError: error?.message || 'Unknown original error',
						processingError: error?.message || 'Unknown processing error',
						stack: error?.stack,
					});
				} catch (logError) {
					console.error('Failed to log critical error:', logError);
				}
			}

			return {
				InvoiceID: '',
				Message: fallbackMessage,
				Status: 500,
			};
		}
	}

	//  catch (error: any) {
	// 	// Handle HTTP errors (400, 401, 403, etc.) that contain Xero validation errors
	// 	if (error.response?.data) {
	// 		const errorData = error.response.data;

	// 		// Check if this is a Xero validation error response
	// 		if (errorData.ErrorNumber && errorData.Elements) {
	// 			// Extract validation errors from Elements array
	// 			const validationErrors: string[] = [];
	// 			const detailedErrors: any[] = [];

	// 			errorData.Elements.forEach((element: any) => {
	// 				if (
	// 					element.ValidationErrors &&
	// 					Array.isArray(element.ValidationErrors)
	// 				) {
	// 					element.ValidationErrors.forEach((validationError: any) => {
	// 						if (validationError.Message) {
	// 							validationErrors.push(validationError.Message);
	// 						}
	// 					});
	// 					detailedErrors.push({
	// 						Type: element.Type,
	// 						InvoiceID: element.InvoiceID,
	// 						Reference: element.Reference,
	// 						ValidationErrors: element.ValidationErrors,
	// 					});
	// 				}
	// 			});

	// 			const errorMessage =
	// 				validationErrors.length > 0
	// 					? validationErrors.join(', ')
	// 					: errorData.Message || 'Validation error occurred';

	// 			// Log detailed validation errors to syncHistory
	// 			if (synclogId) {
	// 				await logSyncHistory(
	// 					synclogId,
	// 					'error',
	// 					`Xero API validation error: ${errorMessage}`,
	// 					{
	// 						Type: errorData.Type || 'ValidationException',
	// 						Message: errorData.Message || 'A validation exception occurred',
	// 						ErrorNumber: errorData.ErrorNumber,
	// 						Elements: detailedErrors,
	// 						httpStatus: error.response.status,
	// 						fullErrorResponse: errorData,
	// 					}
	// 				);
	// 			}

	// 			return {
	// 				InvoiceID: '',
	// 				Message: errorMessage,
	// 				Status: error.response.status || 400,
	// 			};
	// 		}
	// 	}

	// 	// Handle other types of errors (network, timeout, etc.)
	// 	const errorMessage =
	// 		error.response?.data?.message ||
	// 		error.message ||
	// 		'Unknown error occurred';

	// 	if (synclogId) {
	// 		await logSyncHistory(
	// 			synclogId,
	// 			'error',
	// 			`Error creating invoice in Xero: ${errorMessage}`,
	// 			{
	// 				error: errorMessage,
	// 				httpStatus: error.response?.status,
	// 				responseData: error.response?.data,
	// 				stack: error.stack,
	// 			}
	// 		);
	// 	}

	// 	return {
	// 		InvoiceID: '',
	// 		Message: errorMessage,
	// 		Status: error.response?.status || 500,
	// 	};
	// }
}

// 5. Create payment in Xero
export async function createXeroPayment(
	invoiceId: string,
	payment: IPayment,
	connection: any,
	synclogId?: string
): Promise<void> {
	if (!connection?.xeroCredentials)
		throw new ApiException({
			status: 500,
			message: 'Missing Xero credentials',
			errorDescription:
				'Critical error: Xero credentials not found in connection object',
		});

	const payload = {
		Invoice: { InvoiceID: invoiceId },
		Account: { Code: XERO_PAYMENT_CODE },
		Date: payment.date,
		Amount: payment.amount,
		Reference: payment.reference,
	};

	console.log(`Creating Xero payment with account code: ${XERO_PAYMENT_CODE}`);
	console.log(`Payment payload:`, JSON.stringify(payload, null, 2));

	try {
		await xeroApiCall('post', '/Payments', connection, { Payments: [payload] });
	} catch (error: any) {
		// Handle Xero validation errors and enhance the error message
		let errorMessage = 'Failed to create payment in Xero';

		if (error.xeroValidationErrors) {
			// Use the structured error information from xeroApiCall
			const validationMessages = error.xeroValidationErrors.validationMessages;

			// Handle different types of Xero errors
			if (error.xeroValidationErrors.type === 'AuthenticationError') {
				errorMessage = `Xero authentication error: ${validationMessages.join(
					', '
				)}`;
			} else {
				errorMessage =
					validationMessages.length > 0
						? `Xero payment validation error: ${validationMessages.join(', ')}`
						: `Xero payment error: ${
								error.xeroValidationErrors.message || 'Unknown error'
						  }`;
			}

			if (synclogId) {
				await logSyncHistory(synclogId, 'error', errorMessage, {
					xeroValidationErrors: error.xeroValidationErrors,
					paymentPayload: payload,
				});
			}
		} else {
			// Handle other types of errors (network, etc.)
			errorMessage = `Failed to create payment in Xero: ${error.message}`;

			if (synclogId) {
				await logSyncHistory(synclogId, 'error', errorMessage, {
					error: error.message,
					httpStatus: error.response?.status,
					responseData: error.response?.data,
					paymentPayload: payload,
				});
			}
		}

		// Create a new error with the enhanced message
		const enhancedError = new Error(errorMessage);
		enhancedError.stack = error.stack;
		throw enhancedError;
	}
}

// Enhanced function to check HubSpot payment status and create payment in Xero if needed
async function handleInvoicePaymentSync(
	xeroInvoiceId: string,
	hsInvoice: IHubSpotInvoice,
	connection: any,
	synclogId?: string
): Promise<void> {
	console.log(`Checking payment status for HubSpot invoice ${hsInvoice.id}...`);

	// First, try to get payment details from HubSpot commerce_payments API
	let paymentDetails = null;
	let paymentAmount = null;
	let paymentDate = null;
	let paymentRef = null;

	try {
		// Get payment associations for the invoice
		const paymentIds = await hubspotApi.getInvoicePaymentAssociations(
			hsInvoice.id,
			connection
		);

		if (paymentIds.length > 0) {
			// Get details for the first payment (assuming one payment per invoice for now)
			const paymentId = paymentIds[0];
			paymentDetails = await hubspotApi.getCommercePaymentDetails(
				paymentId,
				connection
			);

			if (paymentDetails?.properties) {
				paymentAmount =
					paymentDetails?.properties.hs_initial_amount ||
					hsInvoice?.properties?.hs_amount_billed;
				paymentDate = paymentDetails?.properties.hs_createdate;
				paymentRef = paymentDetails?.properties.hs_reference_number;

				console.log(
					`Found payment details from commerce_payments API for invoice ${hsInvoice.id}:`,
					{
						amount: paymentAmount,
						date: paymentDate,
						reference: paymentRef,
						status: paymentDetails?.properties.status,
					}
				);
			}
		}
	} catch (error: any) {
		console.log(
			`Error fetching payment details from commerce_payments API: ${error.message}`
		);
	}

	// Fallback to invoice properties if no payment details found
	if (!paymentAmount) {
		console.log(
			`No payment details found via commerce_payments API, checking invoice properties...`
		);
		const paymentStatus = paymentDetails?.properties.hs_latest_status;
		const invoiceStatus =
			paymentDetails?.properties?.hs_initial_amount ||
			hsInvoice?.properties?.hs_invoice_status;
		paymentAmount = hsInvoice?.properties?.hs_payment_amount;
		paymentDate =
			paymentDetails?.properties.hs_createdate ||
			hsInvoice?.properties?.hs_payment_date;
		paymentRef =
			paymentDetails?.properties.hs_reference_number ||
			hsInvoice?.properties?.hs_payment_ref_no;

		// Check various indicators that the invoice is paid in HubSpot
		const isPaidInHubSpot =
			(paymentStatus && paymentStatus.toLowerCase() === 'paid') ||
			(invoiceStatus && invoiceStatus.toLowerCase() === 'paid') ||
			(paymentAmount && parseFloat(paymentAmount) > 0);

		if (!isPaidInHubSpot) {
			console.log(
				`Invoice ${hsInvoice.id} is not marked as paid in HubSpot. Skipping payment creation.`
			);
			return;
		}
	}

	// Check if we have valid payment amount
	const amount = paymentAmount ? parseFloat(paymentAmount) : 0;
	if (amount <= 0) {
		console.log(
			`Invoice ${hsInvoice.id} has no valid payment amount. Skipping payment creation.`
		);
		return;
	}

	console.log(
		`Invoice ${hsInvoice.id} has payment amount ${amount}. Creating payment in Xero...`
	);

	if (synclogId) {
		await logSyncHistory(
			synclogId,
			'pending',
			`Creating payment in Xero for paid HubSpot invoice ${hsInvoice.id}`
		);
	}

	// Determine payment date (use payment date if available, otherwise current date)
	const date = paymentDate
		? new Date(paymentDate).toISOString().split('T')[0]
		: new Date().toISOString().split('T')[0];

	// Create payment object
	const payment: IPayment = {
		amount: amount,
		date: date,
		reference: paymentRef || `Payment for ${hsInvoice.id}`,
	};

	// Create payment in Xero
	await createXeroPayment(xeroInvoiceId, payment, connection, synclogId);

	const successMessage = `Payment created in Xero for invoice ${xeroInvoiceId} (Amount: ${amount}, Date: ${date})`;
	console.log(successMessage);

	if (synclogId) {
		await logSyncHistory(synclogId, 'success', successMessage);
	}
}

// 6. Resolve Xero contact
export async function resolveXeroContact(hs: IHubSpotInvoice): Promise<string> {
	const contact = await prisma.hubspotContacts.findFirst({
		where: { customerId: hs.contactId },
	});

	if (contact?.xeroContactId) return contact.xeroContactId;

	const connection = await prisma.connection.findFirst({
		where: { xeroCredentials: { not: Prisma.JsonNull } },
	});
	const { access_token } = connection?.xeroCredentials as any;

	const payload = {
		Name: contact?.email || 'unknown-contact',
		EmailAddress: contact?.email || '',
	};

	const response = await axios.put(
		`${XERO_API_BASE}/Contacts`,
		{ Contacts: [payload] },
		{
			headers: {
				Authorization: `Bearer ${access_token}`,
				'Xero-tenant-id': getXeroTenantId(connection),
				Accept: 'application/json',
				'Content-Type': 'application/json',
			},
		}
	);

	const newContactId = response.data.Contacts[0].ContactID;

	await prisma.hubspotContacts.update({
		where: { id: contact?.id },
		data: { xeroContactId: newContactId },
	});

	return newContactId;
}

// SyncHistory logging function
async function logSyncHistory(
	synclogId: string,
	status: 'pending' | 'success' | 'error',
	message: string,
	description?: any
): Promise<void> {
	// Get current synclog to retrieve existing syncHistory
	const currentSynclog = await prisma.synclog.findUnique({
		where: { id: synclogId },
		select: { syncHistory: true },
	});

	if (!currentSynclog) {
		console.error(`Synclog with ID ${synclogId} not found`);
		return;
	}

	// Parse existing syncHistory or initialize as empty array
	let existingSyncHistory: any[] = [];
	if (currentSynclog.syncHistory) {
		existingSyncHistory = Array.isArray(currentSynclog.syncHistory)
			? currentSynclog.syncHistory
			: [];
	}

	// Create new sync history entry
	const newEntry = {
		status,
		message,
		timeStamp: new Date().toISOString(),
		...(description && { description }),
	};

	// Append new entry to existing history
	const updatedSyncHistory = [...existingSyncHistory, newEntry];

	// Update the synclog with new syncHistory
	await prisma.synclog.update({
		where: { id: synclogId },
		data: {
			syncHistory: updatedSyncHistory,
		},
	});

	console.log(`Sync history logged: ${status} - ${message}`);
}

// 7. Log sync result - Updated to use syncHistory
export async function logSyncResult(
	hsInvoiceId: string,
	status: 'error' | 'pending' | 'success',
	message: string,
	description?: any
): Promise<void> {
	const invoice = await prisma.invoices.findFirst({
		where: { hubSpotInvoiceId: hsInvoiceId },
	});
	if (!invoice) return;

	// Update synclog status and error
	await prisma.synclog.update({
		where: { id: invoice.synclogId },
		data: {
			syncStatus: status,
			processingError: status === 'error' ? message : null,
		},
	});

	// Log to syncHistory
	const syncStatus = status === 'error' ? 'error' : 'success';
	await logSyncHistory(invoice.synclogId, syncStatus, message, description);
}

// Resolve contact ID from various HubSpot sources
async function resolveContactIdFromHubSpot(
	hsInvoice: IHubSpotInvoice,
	connection: any
): Promise<string | null> {
	console.log(`Resolving contact ID for invoice ${hsInvoice.id}...`);

	// Step 1: Try invoice associations first - use API call to get fresh data
	const invoiceContactId = await hubspotApi.getInvoiceContactAssociations(
		hsInvoice.id,
		connection
	);
	if (invoiceContactId) {
		console.log(
			`Found contact ID from invoice associations: ${invoiceContactId}`
		);
		return invoiceContactId;
	}

	// Step 2: Try deal associations - fetch deal ID from invoice associations
	const dealId = await hubspotApi.getInvoiceDealAssociations(
		hsInvoice.id,
		connection
	);
	if (dealId) {
		console.log(`Found deal ID from invoice associations: ${dealId}`);
		const dealAssociations = await hubspotApi.getDealAssociations(
			dealId,
			['contacts'],
			connection
		);
		const dealContactId = dealAssociations?.id ?? null;
		if (dealContactId) {
			console.log(`Found contact ID from deal associations: ${dealContactId}`);
			return dealContactId;
		}
	}

	// Step 3: Try quote associations - fetch quote ID from invoice associations
	const quoteId = await hubspotApi.getInvoiceQuoteAssociations(
		hsInvoice.id,
		connection
	);
	if (quoteId) {
		console.log(`Found quote ID from invoice associations: ${quoteId}`);
		const quoteAssociations = await hubspotApi.getQuoteAssociations(
			quoteId,
			['contacts'],
			connection
		);
		const quoteContactId = quoteAssociations?.contacts?.[0]?.id ?? null;
		if (quoteContactId) {
			console.log(
				`Found contact ID from quote associations: ${quoteContactId}`
			);
			return quoteContactId;
		}
	}

	console.log(`No contact ID found for invoice ${hsInvoice.id}`);
	return null;
}

// Fetch and process HubSpot contact details
async function fetchHubSpotContactDetails(
	contactId: string,
	connection: any
): Promise<{
	wooCustomerJson: any;
	xeroContactId: string | null;
	email: string;
}> {
	console.log(`Fetching HubSpot contact details for contact ${contactId}...`);

	const hubspotContact = await hubspotApi.getContact(contactId, connection);

	const email = hubspotContact?.properties?.email ?? '';
	const firstName = hubspotContact?.properties?.firstname ?? '';
	const lastName = hubspotContact?.properties?.lastname ?? '';
	const name = `${firstName} ${lastName}`.trim();

	const wooCustomerJson = {
		city: hubspotContact?.properties.city || '',
		name,
		email,
		phone: hubspotContact?.properties.phone || '',
		state: hubspotContact?.properties.state || '',
		company: hubspotContact?.properties.company || '',
		country: hubspotContact?.properties.country || '',
		postcode: hubspotContact?.properties.zip || '',
		address_1: hubspotContact?.properties.address || '',
		address_2: '',
		first_name: firstName,
		last_name: lastName,
	};

	// Resolve Xero Contact
	let xeroContactId: string | null = null;
	const contactResult = await findOrCreateXeroContact({
		email,
		firstName,
		lastName,
		companyName: hubspotContact?.properties?.company || '',
		connection,
		invoiceProperties: undefined, // No invoice properties available in this context
	});

	if (!contactResult.contactId) {
		console.error(
			`Failed to create/find Xero contact for HubSpot contact ${contactId}`
		);
		console.error(`   - Error: ${contactResult.error || 'Unknown error'}`);
		console.error(`   - This will cause the invoice to be skipped`);
	} else {
		xeroContactId = contactResult.contactId;
		console.log(`Resolved Xero contact ID: ${xeroContactId}`);
	}
	console.log(`Successfully fetched contact details for ${email}`);

	return { wooCustomerJson, xeroContactId, email };
}

// Fetch HubSpot contact details for database storage only (no Xero operations)
async function fetchHubSpotContactDetailsForDatabase(
	contactId: string,
	connection: any
): Promise<{
	wooCustomerJson: any;
	email: string;
}> {
	console.log(
		`Fetching HubSpot contact details for database storage - contact ${contactId}...`
	);

	const hubspotContact = await hubspotApi.getContact(contactId, connection);

	const email = hubspotContact?.properties?.email ?? '';
	const firstName = hubspotContact?.properties?.firstname ?? '';
	const lastName = hubspotContact?.properties?.lastname ?? '';
	const name = `${firstName} ${lastName}`.trim();

	const wooCustomerJson = {
		city: hubspotContact?.properties.city || '',
		name,
		email,
		phone: hubspotContact?.properties.phone || '',
		state: hubspotContact?.properties.state || '',
		company: hubspotContact?.properties.company || '',
		country: hubspotContact?.properties.country || '',
		postcode: hubspotContact?.properties.zip || '',
		address_1: hubspotContact?.properties.address || '',
		address_2: '',
		first_name: firstName,
		last_name: lastName,
	};

	console.log(
		`Successfully fetched HubSpot contact details for database storage - ${email}`
	);
	return { wooCustomerJson, email };
}

// Update HubSpot contacts table for database storage only (no Xero operations)
async function updateHubSpotContactsTableForDatabase(
	email: string,
	contactId: string,
	dealId: string | null
): Promise<void> {
	if (!email) {
		console.log(`No email provided, skipping HubSpot contacts table update`);
		return;
	}

	const existingContact = await prisma.hubspotContacts.findUnique({
		where: { email },
	});

	if (existingContact) {
		await prisma.hubspotContacts.update({
			where: { email },
			data: {
				customerId: contactId,
				dealId: dealId,
				// Note: xeroContactId will be updated later during contact processing phase
			},
		});
		console.log(`Updated HubSpot contact in database: ${email}`);
	} else {
		await prisma.hubspotContacts.create({
			data: {
				email,
				customerId: contactId,
				dealId: dealId,
				xeroContactId: null, // Will be updated later during contact processing phase
			},
		});
		console.log(`Created new HubSpot contact in database: ${email}`);
	}
}

// Update existing invoice
async function updateExistingInvoice(
	invoiceId: string,
	hsInvoice: IHubSpotInvoice,
	contactId: string | null,
	wooCustomerJson: any,
	xeroContactId: string | null,
	connection: any
): Promise<any> {
	try {
		// Fetch quote ID and deal ID from invoice associations
		const quoteId = await getQuoteIdForInvoice(hsInvoice.id, connection);
		const dealId = await getDealIdForInvoice(hsInvoice.id, connection);
		// Calculate line items total for comparison with detailed logging
		let lineItemsTotal = 0;
		if (hsInvoice.lineItems?.length > 0) {
			console.log(
				` Line Items Breakdown for Invoice ${hsInvoice.id} (UPDATE):`
			);
			hsInvoice.lineItems.forEach((item, index) => {
				const itemTotal = item.quantity * item.unitPrice;
				lineItemsTotal += itemTotal;
				console.log(`   ${index + 1}. ${item.description || 'No description'}`);
				console.log(
					`      Quantity: ${item.quantity} × Unit Price: ${item.unitPrice} = ${itemTotal}`
				);
			});
			console.log(`   Total calculated from line items: ${lineItemsTotal}`);
		}

		// Log all available amount fields for debugging
		console.log(` Amount Analysis for Invoice ${hsInvoice.id} (UPDATE):`);
		console.log(`   - hsInvoice.amount: ${hsInvoice.amount}`);
		console.log(
			`   - hsInvoice?.properties?.amount: ${hsInvoice?.properties?.amount}`
		);
		console.log(
			`   - hsInvoice?.properties?.hs_amount_billed: ${hsInvoice?.properties?.hs_amount_billed}`
		);
		console.log(
			`   - hsInvoice?.properties?.hs_total_amount: ${hsInvoice?.properties?.hs_total_amount}`
		);
		console.log(`   - Calculated from line items: ${lineItemsTotal}`);
		console.log(`   - Line items count: ${hsInvoice.lineItems?.length || 0}`);

		// Determine the best amount to use (prefer calculated total if line items exist)
		let bestAmount: string | null = null;

		if (hsInvoice.lineItems?.length > 0 && lineItemsTotal > 0) {
			bestAmount = lineItemsTotal.toString();
			console.log(`   Using calculated line items total: ${bestAmount}`);

			// Check for discrepancies and log them
			const hsAmountBilled = parseFloat(
				hsInvoice?.properties?.hs_amount_billed || '0'
			);
			const hsAmount = parseFloat(hsInvoice.amount?.toString() || '0');

			if (
				hsAmountBilled > 0 &&
				Math.abs(lineItemsTotal - hsAmountBilled) > 0.01
			) {
				console.log(
					`DISCREPANCY: Line items total (${lineItemsTotal}) ≠ hs_amount_billed (${hsAmountBilled})`
				);
			}
			if (hsAmount > 0 && Math.abs(lineItemsTotal - hsAmount) > 0.01) {
				console.log(
					`DISCREPANCY: Line items total (${lineItemsTotal}) ≠ hsInvoice.amount (${hsAmount})`
				);
			}
		} else if (hsInvoice?.properties?.hs_amount_billed) {
			bestAmount = hsInvoice?.properties.hs_amount_billed.toString();
			console.log(`   Using hs_amount_billed: ${bestAmount}`);
		} else if (hsInvoice.amount) {
			bestAmount = hsInvoice.amount.toString();
			console.log(`   Using hsInvoice.amount: ${bestAmount}`);
		} else if (hsInvoice?.properties?.amount) {
			bestAmount = hsInvoice?.properties.amount.toString();
			console.log(`   Using properties.amount: ${bestAmount}`);
		} else {
			console.log(`No amount found, using null`);
		}

		const updateData = {
			hubSpotContactId: contactId ?? null,
			hubSpotDealId: dealId ?? null, // Use the fetched dealId from associations
			hubSpotQuoteId: quoteId ?? null,
			hubSpotInvoiceNumber: hsInvoice?.properties?.hs_number ?? null,
			invoiceStatus: hsInvoice?.properties?.hs_invoice_status ?? 'PENDING',
			hubSpotPaymentStatus: hsInvoice?.properties?.hs_payment_status ?? null,
			hubSpotPaymentId: hsInvoice?.properties?.hs_payment_id ?? null,
			hubSpotPaymentRefNo: hsInvoice?.properties?.hs_payment_ref_no ?? null,
			hubSpotPaymentNotes: hsInvoice?.properties?.hs_payment_notes ?? null,
			wooCommerceAmount: bestAmount,
			wooCommerceCustomer: wooCustomerJson,
			xeroContactId,
		};

		console.log(
			` Updating invoice data: wooCommerceAmount=${updateData.wooCommerceAmount}, hubSpotQuoteId=${updateData.hubSpotQuoteId}`
		);

		return await prisma.invoices.update({
			where: { id: invoiceId },
			data: updateData,
		});
	} catch (error: any) {
		console.error(`Error updating existing invoice:`, error.message);
		throw new ApiException({
			status: 500,
			message: 'Failed to update existing invoice',
			errorDescription: `Database error: Failed to update existing invoice - ${error.message}`,
		});
	}
}

// Create new invoice
async function createNewInvoice(
	hsInvoice: IHubSpotInvoice,
	contactId: string | null,
	wooCustomerJson: any,
	xeroContactId: string | null,
	syncLogId: string,
	connection: any
): Promise<any> {
	try {
		// Fetch quote ID and deal ID from invoice associations
		const quoteId = await getQuoteIdForInvoice(hsInvoice.id, connection);
		const dealId = await getDealIdForInvoice(hsInvoice.id, connection);
		// Calculate line items total for comparison with detailed logging
		let lineItemsTotal = 0;
		if (hsInvoice.lineItems?.length > 0) {
			console.log(` Line Items Breakdown for Invoice ${hsInvoice.id}:`);
			hsInvoice.lineItems.forEach((item, index) => {
				const itemTotal = item.quantity * item.unitPrice;
				lineItemsTotal += itemTotal;
				console.log(`   ${index + 1}. ${item.description || 'No description'}`);
				console.log(
					`      Quantity: ${item.quantity} × Unit Price: ${item.unitPrice} = ${itemTotal}`
				);
			});
			console.log(`   Total calculated from line items: ${lineItemsTotal}`);
		}

		// Log all available amount fields for debugging
		console.log(` Amount Analysis for Invoice ${hsInvoice.id}:`);
		console.log(`   - hsInvoice.amount: ${hsInvoice.amount}`);
		console.log(
			`   - hsInvoice?.properties?.amount: ${hsInvoice?.properties?.amount}`
		);
		console.log(
			`   - hsInvoice?.properties?.hs_amount_billed: ${hsInvoice?.properties?.hs_amount_billed}`
		);
		console.log(
			`   - hsInvoice?.properties?.hs_total_amount: ${hsInvoice?.properties?.hs_total_amount}`
		);
		console.log(`   - Calculated from line items: ${lineItemsTotal}`);
		console.log(`   - Line items count: ${hsInvoice.lineItems?.length || 0}`);

		// Determine the best amount to use (prefer calculated total if line items exist)
		let bestAmount: string | null = null;

		// Priority 1: Use hs_amount_billed if present
		if (hsInvoice?.properties?.hs_amount_billed) {
			bestAmount = hsInvoice.properties.hs_amount_billed.toString();
			console.log(`   Using hs_amount_billed as first priority: ${bestAmount}`);
		} else if (hsInvoice.lineItems?.length > 0 && lineItemsTotal > 0) {
			bestAmount = lineItemsTotal.toString();
			console.log(`   Using calculated line items total: ${bestAmount}`);

			// Check for discrepancies and log them
			const hsAmountBilled = parseFloat(
				hsInvoice?.properties?.hs_amount_billed || '0'
			);
			const hsAmount = parseFloat(hsInvoice.amount?.toString() || '0');

			if (
				hsAmountBilled > 0 &&
				Math.abs(lineItemsTotal - hsAmountBilled) > 0.01
			) {
				console.log(
					`   DISCREPANCY: Line items total (${lineItemsTotal}) ≠ hs_amount_billed (${hsAmountBilled})`
				);
			}
			if (hsAmount > 0 && Math.abs(lineItemsTotal - hsAmount) > 0.01) {
				console.log(
					`   DISCREPANCY: Line items total (${lineItemsTotal}) ≠ hsInvoice.amount (${hsAmount})`
				);
			}
		} else if (hsInvoice?.properties?.hs_amount_billed) {
			bestAmount = hsInvoice?.properties.hs_amount_billed.toString();
			console.log(`   Using hs_amount_billed: ${bestAmount}`);
		} else if (hsInvoice.amount) {
			bestAmount = hsInvoice.amount.toString();
			console.log(`   Using hsInvoice.amount: ${bestAmount}`);
		} else if (hsInvoice?.properties?.amount) {
			bestAmount = hsInvoice?.properties.amount.toString();
			console.log(`   Using properties.amount: ${bestAmount}`);
		} else {
			console.log(`    No amount found, using null`);
		}

		const invoiceData = {
			hubSpotInvoiceId: hsInvoice.id,
			hubSpotContactId: contactId ?? null,
			hubSpotDealId: dealId ?? null, // Use the fetched dealId from associations
			hubSpotQuoteId: quoteId ?? null,
			hubSpotInvoiceNumber: hsInvoice?.properties?.hs_number ?? null,
			invoiceStatus: hsInvoice?.properties?.hs_invoice_status ?? 'PENDING',
			wooCommerceAmount: bestAmount,
			wooCommerceCustomer: wooCustomerJson,
			xeroContactId,
			synclogId: syncLogId,
		};

		console.log(
			` Storing invoice data: wooCommerceAmount=${invoiceData.wooCommerceAmount}, hubSpotQuoteId=${invoiceData.hubSpotQuoteId}`
		);

		return await prisma.invoices.create({
			data: invoiceData,
		});
	} catch (error: any) {
		console.error(`Error creating new invoice:`, error.message);
		throw new ApiException({
			status: 500,
			message: 'Failed to create new invoice',
			errorDescription: `Database error: Failed to create new invoice - ${error.message}`,
		});
	}
}

// Create sync log
async function createSyncLog(
	connection: any,
	hsInvoice: IHubSpotInvoice,
	contactId: string | null
): Promise<any> {
	try {
		// Fetch quote ID and deal ID from invoice associations
		const quoteId = await getQuoteIdForInvoice(hsInvoice.id, connection);
		const dealId = await getDealIdForInvoice(hsInvoice.id, connection);

		return await prisma.synclog.create({
			data: {
				connectionId: connection.id,
				hubspotInvoiceId: hsInvoice.id,
				hubspotContactId: contactId ?? null,
				hubspotDealId: dealId ?? null, // Use the fetched dealId from associations
				hubspotQuoteId: quoteId ?? null,
				hubSpotInvoiceNumber: hsInvoice?.properties?.hs_number ?? null,
				syncStatus: hsInvoice?.properties?.hs_invoice_status ?? null,
			},
		});
	} catch (error: any) {
		console.error(`Error creating sync log:`, error.message);
		throw new ApiException({
			status: 500,
			message: 'Failed to create sync log',
			errorDescription: `Database error: Failed to create sync log - ${error.message}`,
		});
	}
}

// Update or create sync log for existing invoice
async function updateOrCreateSyncLog(
	connection: any,
	hsInvoice: IHubSpotInvoice,
	contactId: string | null
): Promise<void> {
	try {
		const existingSyncLog = await prisma.synclog.findFirst({
			where: {
				connectionId: connection.id,
				hubspotInvoiceId: hsInvoice.id,
			},
		});

		if (existingSyncLog) {
			// Fetch quote ID and deal ID from invoice associations
			const quoteId = await getQuoteIdForInvoice(hsInvoice.id, connection);
			const dealId = await getDealIdForInvoice(hsInvoice.id, connection);

			await prisma.synclog.update({
				where: { id: existingSyncLog.id },
				data: {
					hubspotContactId: contactId ?? null,
					hubspotDealId: dealId ?? null, // Use the fetched dealId from associations
					hubspotQuoteId: quoteId ?? null,
					hubSpotInvoiceNumber: hsInvoice?.properties?.hs_number ?? null,
					syncStatus: hsInvoice?.properties?.hs_invoice_status ?? null,
				},
			});
		} else {
			await createSyncLog(connection, hsInvoice, contactId);
		}
	} catch (error: any) {
		console.error(`Error updating/creating sync log:`, error.message);
		throw new ApiException({
			status: 500,
			message: 'Failed to update/create sync log',
			errorDescription: `Database error: Failed to update/create sync log - ${error.message}`,
		});
	}
}

// Fetch line items from HubSpot
async function fetchLineItemsFromHubSpot(
	lineItemIds: string[],
	connection?: any
): Promise<any[]> {
	try {
		console.log(`Fetching ${lineItemIds.length} line items from HubSpot...`);

		// Log each line item ID being fetched
		lineItemIds.forEach((id) => {
			console.log(`Fetching details for line item ID: ${id}`);
		});

		// Get API key from connection or environment
		let apiKey = process.env.HUBSPOT_API_KEY;
		if (connection?.hubSpotCredentials?.hapikey) {
			apiKey = connection.hubSpotCredentials.hapikey;
		}

		const lineItemsResponse = await axios.post(
			`https://api.hubapi.com/crm/v3/objects/line_items/batch/read`,
			{
				properties: [
					'name',
					'quantity',
					'price',
					'amount',
					'hs_discount_percentage',
					'hs_product_id',
					'item_code',
					'tax_code',
				],
				inputs: lineItemIds.map((id: string) => ({ id })),
			},
			{
				headers: {
					Authorization: `Bearer ${apiKey}`,
					'Content-Type': 'application/json',
				},
			}
		);

		// Log success for each line item
		lineItemsResponse.data.results.forEach((item: any) => {
			console.log(
				`Successfully retrieved details for line item ID: ${item.id}`
			);
		});

		console.log(
			`Successfully fetched ${lineItemsResponse.data.results.length} line items`
		);
		return lineItemsResponse.data.results;
	} catch (error: any) {
		console.error(`Error fetching line items from HubSpot:`, error.message);
		throw new ApiException({
			status: 500,
			message: 'Failed to fetch line items',
			errorDescription: `API error: Failed to fetch line items from HubSpot - ${error.message}`,
		});
	}
}

// Date validation helper function
function validateDateRange(fromDate?: string, toDate?: string): void {
	if (!fromDate && !toDate) {
		return; // No dates provided, skip validation
	}

	// Validate date format (YYYY-MM-DD)
	const dateRegex = /^\d{4}-\d{2}-\d{2}$/;

	if (fromDate && !dateRegex.test(fromDate)) {
		throw new ApiException({
			status: 400,
			message: 'Invalid fromDate format',
			errorDescription:
				'fromDate must be in YYYY-MM-DD format (e.g., 2025-07-18)',
		});
	}

	if (toDate && !dateRegex.test(toDate)) {
		throw new ApiException({
			status: 400,
			message: 'Invalid toDate format',
			errorDescription:
				'toDate must be in YYYY-MM-DD format (e.g., 2025-07-20)',
		});
	}

	// Validate that dates are valid dates
	if (fromDate) {
		const fromDateObj = new Date(fromDate);
		if (isNaN(fromDateObj.getTime())) {
			throw new ApiException({
				status: 400,
				message: 'Invalid fromDate',
				errorDescription: `fromDate "${fromDate}" is not a valid date`,
			});
		}
	}

	if (toDate) {
		const toDateObj = new Date(toDate);
		if (isNaN(toDateObj.getTime())) {
			throw new ApiException({
				status: 400,
				message: 'Invalid toDate',
				errorDescription: `toDate "${toDate}" is not a valid date`,
			});
		}
	}

	// Validate date range (fromDate should not be after toDate)
	if (fromDate && toDate) {
		const fromDateObj = new Date(fromDate);
		const toDateObj = new Date(toDate);

		if (fromDateObj > toDateObj) {
			throw new ApiException({
				status: 400,
				message: 'Invalid date range',
				errorDescription: `fromDate (${fromDate}) cannot be after toDate (${toDate}). Please ensure fromDate is earlier than or equal to toDate.`,
			});
		}
	}

	// Validate that dates are not too far in the future
	const currentDate = new Date();
	const maxFutureDate = new Date();
	maxFutureDate.setFullYear(currentDate.getFullYear() + 2); // Allow up to 2 years in the future

	if (fromDate) {
		const fromDateObj = new Date(fromDate);
		if (fromDateObj > maxFutureDate) {
			throw new ApiException({
				status: 400,
				message: 'fromDate too far in future',
				errorDescription: `fromDate "${fromDate}" is too far in the future. Maximum allowed date is ${
					maxFutureDate.toISOString().split('T')[0]
				}`,
			});
		}
	}

	if (toDate) {
		const toDateObj = new Date(toDate);
		if (toDateObj > maxFutureDate) {
			throw new ApiException({
				status: 400,
				message: 'toDate too far in future',
				errorDescription: `toDate "${toDate}" is too far in the future. Maximum allowed date is ${
					maxFutureDate.toISOString().split('T')[0]
				}`,
			});
		}
	}

	// Validate that date range is not too large (prevent performance issues)
	if (fromDate && toDate) {
		const fromDateObj = new Date(fromDate);
		const toDateObj = new Date(toDate);
		const daysDifference = Math.ceil(
			(toDateObj.getTime() - fromDateObj.getTime()) / (1000 * 60 * 60 * 24)
		);

		const maxDaysRange = 365; // Maximum 1 year range
		if (daysDifference > maxDaysRange) {
			throw new ApiException({
				status: 400,
				message: 'Date range too large',
				errorDescription: `Date range of ${daysDifference} days exceeds maximum allowed range of ${maxDaysRange} days. Please use a smaller date range.`,
			});
		}
	}
}

export async function syncHubSpotInvoicesToXero(query: {
	fromDate?: string;
	toDate?: string;
	page?: number;
}) {
	console.log(' DEBUG: Starting HubSpot to Xero sync...');

	// Check if sync is already in progress
	if (syncInProgress) {
		const runningTime = syncStartTime ? Date.now() - syncStartTime : 0;
		const message = `Sync already in progress for ${Math.round(runningTime / 1000)} seconds. Please wait for it to complete.`;
		console.log(message);

		// Emit status via socket if available
		if ((global as any).io) {
			(global as any).io.emit('sync-status', {
				message,
				status: 'already-running',
				runningTimeSeconds: Math.round(runningTime / 1000),
				timestamp: new Date().toISOString()
			});
		}

		throw new ApiException({
			status: 409,
			message: 'Sync already in progress',
			errorDescription: message
		});
	}

	// Mark sync as in progress
	syncInProgress = true;
	syncStartTime = Date.now();

	try {
		// Validate date range first
		validateDateRange(query?.fromDate, query?.toDate);

	console.log('fromDate:', query?.fromDate);
	console.log('toDate:', query?.toDate);

	// Log the validated date range
	if (query?.fromDate || query?.toDate) {
		console.log(
			`Date range validated: ${query.fromDate || 'no start date'} to ${
				query.toDate || 'no end date'
			}`
		);
	}

	// Initialize Xero tokens if needed
	await initializeTokens();

	// Track sync results
	const syncResults = {
		totalProcessed: 0,
		successful: 0,
		failed: 0,
		skipped: 0,
		successfulInvoices: [] as string[],
		failedInvoices: [] as { id: string; error: string }[],
		skippedInvoices: [] as { id: string; reason: string }[],
	};

	const connection = await prisma.connection.findFirst({
		where: { xeroCredentials: { not: Prisma.JsonNull } },
	});

	if (!connection?.id) {
		console.error('No connection with Xero credentials found.');
		throw new ApiException({
			status: 400,
			message: 'Missing Xero credentials',
			errorDescription: 'No connection with Xero credentials found.',
		});
	}

	// Configuration for batch processing
	const isProduction = process.env.NODE_ENV === 'production';
	const batchSize = isProduction ? 50 : 10; // 50 for production, 10 for development

	// Pagination state - use cursor-based pagination with HubSpot's 'after' token
	let currentPage = 1;
	let afterToken: string | undefined = undefined;
	let hasMore = true;

	console.log(
		`Starting sync with batch size: ${batchSize} (${
			isProduction ? 'Production' : 'Development'
		} mode)`
	);

	while (hasMore) {
		console.log(
			`Processing batch ${currentPage} (after token: ${
				afterToken || 'none'
			})...`
		);

		const { invoices, paging } = await getInvoicesFromHubSpot(
			connection,
			afterToken,
			batchSize,
			query
		);

		// Debug: filter invoices by specific IDs
		// const invoices = invoices.filter((invoice: any) =>
		// 	[
		// 		675868816622
		// 		].includes(
		// 		Number(invoice.id)
		// 	)
		// );

		if (!invoices || invoices.length === 0) {
			console.log('No more invoices found in HubSpot.');
			hasMore = false;
			break;
		}

		console.log(`Fetched ${invoices.length} invoices in batch ${currentPage}`);

		for (const hsInvoice of invoices as IHubSpotInvoice[]) {
			console.log(`Processing HubSpot invoice ${hsInvoice.id}...`);

			// Log address information if available
			const addressInfo = hsInvoice.properties;
			if (addressInfo) {
				const addressParts = [
					addressInfo.hs_recipient_company_address,
					addressInfo.hs_recipient_company_address2,
					addressInfo.hs_recipient_company_city,
					addressInfo.hs_recipient_company_state,
					addressInfo.hs_recipient_company_country,
					addressInfo.hs_recipient_company_zip,
				].filter(Boolean);

				if (addressParts.length > 0) {
					console.log(`  Invoice address: ${addressParts.join(', ')}`);
				}
			}

			// Check if this is a re-run by looking for existing invoice
			const existingInvoiceCheck = await prisma.invoices.findFirst({
				where: { hubSpotInvoiceId: hsInvoice.id },
				select: { synclogId: true, xeroInvoiceId: true },
			});

			let isRerun = false;
			let existingSynclogId: string | null = null;

			if (existingInvoiceCheck) {
				isRerun = true;
				existingSynclogId = existingInvoiceCheck.synclogId;

				// Log that this is a re-run to sync history
				if (existingSynclogId) {
					await logSyncHistory(
						existingSynclogId,
						'pending',
						`Re-running sync for HubSpot invoice ${
							hsInvoice.id
						} (existing Xero invoice: ${
							existingInvoiceCheck.xeroInvoiceId || 'none'
						})`
					);
				}
			}

			try {
				// 1. Invoice and Line Items Discovery
				const discoveredLineItems = await discoverInvoiceAndLineItems(
					hsInvoice,
					connection
				);

				// 2. Database Storage (Invoice + Line Items) - Pure database operations only
				await storeInvoiceAndLineItemsInDatabase(
					hsInvoice,
					connection,
					discoveredLineItems
				);

				// Get the synclog ID for this invoice
				const invoiceRecord = await prisma.invoices.findFirst({
					where: { hubSpotInvoiceId: hsInvoice.id },
					select: { synclogId: true },
				});
				const synclogId = invoiceRecord?.synclogId;

				// 2.5. Xero Contact Resolution - Find or create Xero contact and update database
				const contactResolutionResult = await findOrCreateXeroContactForInvoice(
					hsInvoice,
					connection,
					synclogId
				);
				if (!contactResolutionResult.contactId) {
					const errorReason =
						contactResolutionResult.error || 'Xero contact resolution failed';
					console.log(`Skipping invoice ${hsInvoice.id} - ${errorReason}`);

					// Track as skipped with specific error message
					syncResults.skipped++;
					syncResults.skippedInvoices.push({
						id: hsInvoice.id,
						reason: errorReason,
					});
					continue;
				}

				// 3. Contact Processing Flow
				const contactId = await processContactFlow(hsInvoice, connection);
				if (!contactId) {
					console.error(
						`Skipping invoice ${hsInvoice.id} - Contact creation failed due to missing name information`
					);

					// Track as skipped
					syncResults.skipped++;
					syncResults.skippedInvoices.push({
						id: hsInvoice.id,
						reason: 'Contact creation failed due to missing name information',
					});
					continue;
				}

				// 4. Product/Line Item Validation in Xero (only if line items exist)
				if (discoveredLineItems.length > 0) {
					const productValidationSuccess =
						await validateAndCreateProductsInXero(hsInvoice, connection);
					if (!productValidationSuccess) {
						console.log(
							`Skipping invoice ${hsInvoice.id} - Product validation/creation failed`
						);

						// Track as skipped
						syncResults.skipped++;
						syncResults.skippedInvoices.push({
							id: hsInvoice.id,
							reason: 'Product validation/creation failed',
						});
						continue;
					}
				} else {
					console.log(
						`Skipping product validation for invoice ${hsInvoice.id} - No line items found`
					);
				}

				// 5. Xero Invoice Creation
				await createInvoiceInXero(hsInvoice, contactId, connection, synclogId);

				// Track as successful (full Xero sync completed)
				syncResults.successful++;
				syncResults.successfulInvoices.push(hsInvoice.id);

				// Log final success to sync history and update sync log status
				if (synclogId) {
					await logSyncHistory(
						synclogId,
						'success',
						`Successfully completed sync for HubSpot invoice ${hsInvoice.id}${
							isRerun ? ' (re-run)' : ''
						}`
					);

					// Update sync log status to success
					await prisma.synclog.update({
						where: { id: synclogId },
						data: {
							syncStatus: 'success',
							processingError: null,
							updatedAt: new Date(),
						},
					});
				}

				// 6. Future sync history logging (mentioned for future implementation)
				console.log(
					`Sync history logging will be added in future implementation`
				);
			} catch (error: any) {
				// 6. Error Handling - Continue with next invoice even if one fails
				console.error(
					`Error processing invoice ${hsInvoice.id}:`,
					error.message
				);

				// Log error to sync history if we have a synclog ID
				if (existingSynclogId) {
					await logSyncHistory(
						existingSynclogId,
						'error',
						`Failed to process HubSpot invoice ${hsInvoice.id}: ${error.message}`,
						{ error: error.message, stack: error.stack }
					);

					// Update sync log status to error
					await prisma.synclog.update({
						where: { id: existingSynclogId },
						data: {
							syncStatus: 'error',
							processingError: error.message,
							updatedAt: new Date(),
						},
					});
				}

				await logSyncResult(hsInvoice.id, 'error', error.message);
				console.log(
					`Continuing with next invoice after error in ${hsInvoice.id}`
				);

				// Track as failed
				syncResults.failed++;
				syncResults.failedInvoices.push({
					id: hsInvoice.id,
					error: error.message,
				});
			} finally {
				// Increment the processed invoices counter regardless of success or failure
				syncResults.totalProcessed++;
				console.log(`Processed ${syncResults.totalProcessed} invoices so far`);
			}
		}

		// Update pagination state for next iteration
		if (paging?.next?.after) {
			afterToken = paging.next.after;
			currentPage++;
			hasMore = true;
			console.log(
				`Moving to batch ${currentPage} with after token: ${afterToken}`
			);
		} else {
			hasMore = false;
			console.log(
				`No more batches to process. Sync complete. Total batches processed: ${currentPage}`
			);
		}
	}

	// Generate final sync summary
	const syncSummary = {
		status: 'completed',
		summary: {
			totalProcessed: syncResults.totalProcessed,
			successful: syncResults.successful,
			failed: syncResults.failed,
			skipped: syncResults.skipped,
		},
		details: {
			successfulInvoices: syncResults.successfulInvoices,
			failedInvoices: syncResults.failedInvoices,
			skippedInvoices: syncResults.skippedInvoices,
		},
		message: `Sync completed: ${syncResults.successful} successful, ${syncResults.failed} failed, ${syncResults.skipped} skipped out of ${syncResults.totalProcessed} total invoices processed.`,
	};

	// Log final summary
	console.log(' HubSpot to Xero sync completed!');
	console.log(`Final Results:`);
	console.log(`   Successful: ${syncResults.successful}`);
	console.log(`   Failed: ${syncResults.failed}`);
	console.log(`   Skipped: ${syncResults.skipped}`);
	console.log(`    Total Processed: ${syncResults.totalProcessed}`);

	if (syncResults.successfulInvoices.length > 0) {
		console.log(
			`    Successful Invoices: ${syncResults.successfulInvoices.join(', ')}`
		);
	}

	if (syncResults.failedInvoices.length > 0) {
		console.log(`   Failed Invoices:`);
		syncResults.failedInvoices.forEach((invoice) => {
			console.log(`      - ${invoice.id}: ${invoice.error}`);
		});
	}

	if (syncResults.skippedInvoices.length > 0) {
		console.log(`   Skipped Invoices:`);
		syncResults.skippedInvoices.forEach((invoice) => {
			console.log(`      - ${invoice.id}: ${invoice.reason}`);
		});
	}

	return {
		responseStatus: 200,
		data: syncSummary,
	};
} finally {
	// Reset sync status
	syncInProgress = false;
	syncStartTime = null;

	// Emit completion status via socket if available
	if ((global as any).io) {
		(global as any).io.emit('sync-completed', {
			message: 'HubSpot to Xero sync completed',
			status: 'completed',
			timestamp: new Date().toISOString()
		});
	}

	console.log(' DEBUG: HubSpot to Xero sync process completed and status reset');
}

// 2.5. Find or Create Xero Contact for Invoice and Update Database
async function findOrCreateXeroContactForInvoice(
	hsInvoice: IHubSpotInvoice,
	connection: any,
	synclogId?: string
): Promise<{ contactId: string | null; error?: string }> {
	console.log(
		` Finding or creating Xero contact for invoice ${hsInvoice.id}...`
	);

	if (synclogId) {
		await logSyncHistory(
			synclogId,
			'pending',
			`Starting process of contact creation/resolution in Xero for invoice ${hsInvoice.id}`
		);
	}

	try {
		// Get contact ID from HubSpot
		const contactId = await resolveContactIdFromHubSpot(hsInvoice, connection);
		if (!contactId) {
			const errorMessage = `No HubSpot contact found for invoice ${hsInvoice.id}`;
			console.log(` ${errorMessage}`);

			if (synclogId) {
				await logSyncHistory(synclogId, 'error', errorMessage);
			}
			return { contactId: null, error: errorMessage };
		}

		// Fetch HubSpot contact details
		const hubspotContact = await hubspotApi.getContact(contactId, connection);
		const email = hubspotContact?.properties?.email ?? '';
		const firstName = hubspotContact?.properties?.firstname ?? '';
		const lastName = hubspotContact?.properties?.lastname ?? '';
		const companyName = hubspotContact?.properties?.company ?? '';

		// Find or create Xero contact
		const contactResult = await findOrCreateXeroContact({
			email,
			firstName,
			lastName,
			companyName,
			connection,
			synclogId,
			invoiceProperties: hsInvoice.properties,
		});

		if (!contactResult.contactId) {
			const errorMessage =
				contactResult.error ||
				`Failed to find/create Xero contact for invoice ${hsInvoice.id}`;
			console.error(errorMessage);

			if (synclogId) {
				await logSyncHistory(synclogId, 'error', errorMessage);
			}
			return { contactId: null, error: errorMessage };
		}

		const xeroContactId = contactResult.contactId;

		// Update the HubSpot contacts table with the Xero contact ID
		if (email) {
			try {
				const existingContact = await prisma.hubspotContacts.findUnique({
					where: { email },
				});

				if (existingContact) {
					await prisma.hubspotContacts.update({
						where: { email },
						data: { xeroContactId },
					});
					console.log(
						`Updated HubSpot contact with Xero ID: ${email} -> ${xeroContactId}`
					);
				} else {
					// CREATE contact if it doesn't exist
					await prisma.hubspotContacts.create({
						data: {
							email,
							customerId: contactId,
							xeroContactId,
						},
					});
					console.log(
						`Created new contact with Xero ID: ${email} -> ${xeroContactId}`
					);
				}
			} catch (error: any) {
				console.error(
					` Failed to update HubSpot contact with Xero ID:`,
					error.message
				);
			}
		}

		console.log(
			`Resolved Xero contact ID for invoice ${hsInvoice.id}: ${xeroContactId}`
		);
		return { contactId: xeroContactId };
	} catch (error: any) {
		const errorMessage = `Error finding/creating Xero contact for invoice ${hsInvoice.id}: ${error.message}`;
		console.error(errorMessage);
		return { contactId: null, error: errorMessage };
	}
}

// 1. Invoice and Line Items Discovery
async function discoverInvoiceAndLineItems(
	hsInvoice: IHubSpotInvoice,
	connection: any
): Promise<IHsLineItem[]> {
	console.log(`Discovering line items for invoice ${hsInvoice.id}...`);

	if (!connection?.hubSpotCredentials) {
		console.error('Missing HubSpot credentials for line item discovery');
		return [];
	}
	const { hapikey } = connection.hubSpotCredentials as any;

	try {
		// Step 1: Get deal ID associated with the invoice
		console.log(
			`Step 1: Getting deal associations for invoice ${hsInvoice.id}...`
		);

		const dealResponse = await axios.get(
			`https://api.hubapi.com/crm/v3/objects/invoice/${hsInvoice.id}/associations/deals`,
			{
				headers: {
					Authorization: `Bearer ${hapikey}`,
				},
			}
		);

		const dealId = dealResponse.data?.results?.[0]?.id;
		let lineItemIds: string[] = [];
		let lineItems: any[] = [];

		if (dealId) {
			console.log(`Found deal ID ${dealId} for invoice ${hsInvoice.id}`);

			// Step 2: Get line items associated with the deal
			console.log(`Step 2: Getting line items for deal ${dealId}...`);

			const lineItemsResponse = await axios.get(
				`https://api.hubapi.com/crm/v3/objects/deals/${dealId}/associations/line_items`,
				{
					headers: {
						Authorization: `Bearer ${hapikey}`,
					},
				}
			);

			lineItemIds =
				lineItemsResponse.data?.results?.map((li: any) => li.id) || [];

			if (lineItemIds.length > 0) {
				console.log(
					`Found ${lineItemIds.length} line items for deal ${dealId}`
				);
				// Step 3: Fetch detailed line item information from HubSpot
				lineItems = await fetchLineItemsFromHubSpot(lineItemIds, connection);
			} else {
				console.log(`No line items found for deal ${dealId}`);
			}
		} else {
			console.log(`No deal associations found for invoice ${hsInvoice.id}`);
		}

		// If no line items found via deal, try direct invoice associations
		if (lineItemIds.length === 0) {
			console.log(
				`Trying direct invoice-to-line-item associations for invoice ${hsInvoice.id}...`
			);

			const directLineItemsResponse = await axios.get(
				`https://api.hubapi.com/crm/v3/objects/invoice/${hsInvoice.id}/associations/line_items`,
				{
					headers: {
						Authorization: `Bearer ${hapikey}`,
					},
				}
			);

			lineItemIds =
				directLineItemsResponse.data?.results?.map((li: any) => li.id) || [];

			if (lineItemIds.length > 0) {
				console.log(
					`Found ${lineItemIds.length} line items via direct invoice associations`
				);
				lineItems = await fetchLineItemsFromHubSpot(lineItemIds, connection);
			} else {
				console.log(
					`No line items found via direct invoice associations either`
				);
				hsInvoice.lineItems = [];
				return [];
			}
		}

		// Transform and return the discovered line items
		const transformedLineItems: IHsLineItem[] = lineItems.map((item: any) => {
			const props = item?.properties || {};
			return {
				id: item.id,
				description: props.name || props.description || 'Service',
				quantity: parseInt(props.quantity || '1', 10),
				unitPrice: parseFloat(props.price || '0'),
				total: parseFloat(props.amount || '0'),
				subTotal: parseFloat(props.amount || '0'),
				name: props.name || null,
				hsProductId: props.hs_product_id || null,
				itemCode: generateItemCode(props.hs_product_id, props.name),
				taxCode: props.tax_code || null,
			};
		});

		// Store line items in the invoice object for backward compatibility
		hsInvoice.lineItems = transformedLineItems;
		return transformedLineItems;
	} catch (error: any) {
		console.error(
			`Error discovering line items for invoice ${hsInvoice.id}:`,
			error.message
		);

		// If there's an error, return empty array
		console.log(
			`No line items could be discovered for invoice ${hsInvoice.id} due to error`
		);
		hsInvoice.lineItems = [];
		return [];
	}
}

// 2. Database Storage (Invoice + Line Items) - PURE DATABASE OPERATIONS ONLY
async function storeInvoiceAndLineItemsInDatabase(
	hsInvoice: IHubSpotInvoice,
	connection: any,
	discoveredLineItems: IHsLineItem[]
): Promise<void> {
	console.log(`Storing invoice ${hsInvoice.id} and line items in database...`);

	// Step 1: Resolve Contact ID from HubSpot (no Xero operations)
	const contactId = await resolveContactIdFromHubSpot(hsInvoice, connection);
	const dealId = hsInvoice.associations?.deals?.results?.[0]?.id ?? null;

	// Step 2: Fetch HubSpot contact details only (no Xero operations)
	let wooCustomerJson: any = {
		name: '',
		first_name: '',
		last_name: '',
	};
	let email = '';

	if (contactId) {
		const contactDetails = await fetchHubSpotContactDetailsForDatabase(
			contactId,
			connection
		);
		wooCustomerJson = contactDetails.wooCustomerJson;
		email = contactDetails.email;

		// Update HubSpot contacts table (database only, no Xero operations)
		await updateHubSpotContactsTableForDatabase(email, contactId, dealId);
	}

	// Step 3: Store invoice in database first and get the invoice record
	const invoiceRecord = await storeInvoiceInDatabase(
		hsInvoice,
		connection,
		contactId || '',
		wooCustomerJson,
		null // No xeroContactId during database storage phase
	);

	// Step 4: Store line items in database using the invoice ID
	await storeLineItemsInDatabase(
		invoiceRecord.id,
		discoveredLineItems,
		hsInvoice.id
	);

	console.log(
		`Invoice ${hsInvoice.id} and line items stored in database successfully`
	);
}

// 2a. Store Invoice in Database
async function storeInvoiceInDatabase(
	hsInvoice: IHubSpotInvoice,
	connection: any,
	contactId: string,
	wooCustomerJson: any,
	xeroContactId: string | null
): Promise<any> {
	try {
		console.log(` Storing invoice ${hsInvoice.id} in Invoices table...`);

		// Check if invoice already exists
		const existingInvoice = await prisma.invoices.findFirst({
			where: { hubSpotInvoiceId: hsInvoice.id },
			include: { InvoiceLines: true },
		});

		let invoiceRecord;

		if (existingInvoice) {
			console.log(`Invoice ${hsInvoice.id} already exists. Updating details.`);

			// Log to sync history that we found existing invoice
			if (existingInvoice.synclogId) {
				await logSyncHistory(
					existingInvoice.synclogId,
					'pending',
					`Found existing invoice ${hsInvoice.id} in database. Updating with latest HubSpot data.`
				);
			}

			invoiceRecord = await updateExistingInvoice(
				existingInvoice.id,
				hsInvoice,
				contactId,
				wooCustomerJson,
				xeroContactId,
				connection
			);
			await updateOrCreateSyncLog(connection, hsInvoice, contactId);
		} else {
			console.log(`Creating new invoice ${hsInvoice.id} in database.`);
			const syncLogRecord = await createSyncLog(
				connection,
				hsInvoice,
				contactId
			);
			invoiceRecord = await createNewInvoice(
				hsInvoice,
				contactId,
				wooCustomerJson,
				xeroContactId,
				syncLogRecord.id,
				connection
			);
		}

		console.log(
			`Invoice ${hsInvoice.id} stored in Invoices table successfully`
		);
		return invoiceRecord;
	} catch (error: any) {
		console.error(
			`Error storing invoice ${hsInvoice.id} in database:`,
			error.message
		);
		throw new ApiException({
			status: 500,
			message: 'Failed to store invoice in database',
			errorDescription: `Database error: Failed to store invoice in database - ${error.message}`,
		});
	}
}

// 2b. Store Line Items in Database
async function storeLineItemsInDatabase(
	invoiceId: string,
	discoveredLineItems: IHsLineItem[],
	hsInvoiceId: string
): Promise<void> {
	try {
		console.log(
			`Storing ${discoveredLineItems.length} line items in InvoiceLines table for invoice ${hsInvoiceId}...`
		);

		// Handle the case where discoveredLineItems is empty
		if (discoveredLineItems.length === 0) {
			console.log(`No line items to store for invoice ${hsInvoiceId}`);
			return;
		}

		// Transform discovered line items to database format matching Prisma schema
		const lineItemsToCreate = discoveredLineItems.map((item: IHsLineItem) => {
			return {
				lineId: item.id, // HubSpot line item ID
				invoiceId: invoiceId, // Foreign key to Invoices table
				description: item.description || null, // Product description
				quantity: item.quantity || 1, // Quantity (Int in schema)
				unitPrice: item.unitPrice || 0, // Unit price (Float in schema)
				totalPrice: item.total || 0, // Total price (Float in schema)
				subTotal: item.subTotal || 0, // Subtotal (Float in schema)
				hubSpotProductId: item.hsProductId || null, // HubSpot product ID
				hubSpotLineId: item.id, // HubSpot line item ID
				sku: `ITEM-${item.hsProductId}`,
				productName: item.name || item.description || null, // Product name
				// Optional fields that can be populated later:
				wooCommerceProductId: null,
				linnWorksInventoryId: null,
				xeroItemId: null,
			};
		});

		// Delete existing line items for this invoice to avoid duplicates
		await prisma.invoiceLines.deleteMany({
			where: { invoiceId: invoiceId },
		});

		// Create new line items in batch
		await prisma.invoiceLines.createMany({
			data: lineItemsToCreate,
			skipDuplicates: true,
		});

		console.log(
			`Successfully stored ${lineItemsToCreate.length} line items in InvoiceLines table for invoice ${hsInvoiceId}`
		);
	} catch (error: any) {
		console.error(
			`Error storing line items for invoice ${hsInvoiceId}:`,
			error.message
		);
		throw new ApiException({
			status: 500,
			message: 'Failed to store line items in database',
			errorDescription: `Database error: Failed to store line items in database - ${error.message}`,
		});
	}
}

// 3. Contact Processing Flow
async function processContactFlow(
	hsInvoice: IHubSpotInvoice,
	connection: any
): Promise<string | null> {
	console.log(` Processing contact flow for invoice ${hsInvoice.id}...`);

	// Fetch contact details from HubSpot
	const contactId = await resolveContactIdFromHubSpot(hsInvoice, connection);
	if (!contactId) {
		console.log(`No contact found for invoice ${hsInvoice.id}`);
		return null;
	}

	const contactDetails = await fetchHubSpotContactDetails(
		contactId,
		connection
	);
	const email = contactDetails.email;
	const firstName = hsInvoice.contactDetails?.firstname || '';
	const lastName = hsInvoice.contactDetails?.lastname || '';
	const companyName = hsInvoice.contactDetails?.company || '';

	// Search for existing contact in Xero by email first
	if (email) {
		const emailResponse = await xeroApiCall(
			'get',
			`/Contacts?where=${encodeURIComponent(`EmailAddress=="${email}"`)}`,
			connection
		);

		if (
			emailResponse?.data?.Contacts &&
			Array.isArray(emailResponse.data.Contacts) &&
			emailResponse.data.Contacts.length > 0
		) {
			const existingContactId = emailResponse.data.Contacts[0].ContactID;
			console.log(`Contact found in Xero with email: ${email}`);
			return existingContactId;
		}
	}

	// Search for existing contact in Xero by contact name
	const nameValidation = validateContactName(firstName, lastName, companyName);
	if (nameValidation.isValid) {
		const contactName = nameValidation.contactName;
		const nameResponse = await xeroApiCall(
			'get',
			`/Contacts?where=${encodeURIComponent(`Name=="${contactName}"`)}`,
			connection
		);

		if (
			nameResponse?.data?.Contacts &&
			Array.isArray(nameResponse.data.Contacts) &&
			nameResponse.data.Contacts.length > 0
		) {
			const existingContactId = nameResponse.data.Contacts[0].ContactID;
			console.log(`Contact found in Xero with contact name: ${contactName}`);
			return existingContactId;
		} else {
			console.log(
				`Contact not found in Xero with contact name: ${contactName}`
			);
		}

		// Create new contact in Xero
		console.log(
			`Creating new contact in Xero with contact name: ${contactName}`
		);
		const addresses = buildXeroAddress(hsInvoice.properties);
		const newContactPayload = {
			Name: contactName,
			...(email && { EmailAddress: email }),
			...(firstName && { FirstName: firstName }),
			...(lastName && { LastName: lastName }),
			...(addresses && { Addresses: addresses }),
		};

		if (addresses) {
			const addr = addresses[0];
			console.log(`Adding POBOX address to Xero contact:`);
			console.log(`  AddressLine1: "${addr.AddressLine1}"`);
			console.log(`  AddressLine2: "${addr.AddressLine2}"`);
			console.log(`  City: "${addr.City}"`);
			console.log(`  Region: "${addr.Region}"`);
			console.log(`  PostalCode: "${addr.PostalCode}"`);
			console.log(`  Country: "${addr.Country}"`);
		}

		try {
			const createResponse = await xeroApiCall(
				'post',
				'/Contacts',
				connection,
				{
					Contacts: [newContactPayload],
				}
			);

			if (
				createResponse?.data?.Contacts &&
				Array.isArray(createResponse.data.Contacts) &&
				createResponse.data.Contacts.length > 0
			) {
				const newContactId = createResponse.data.Contacts[0].ContactID;
				console.log(
					`Contact created in Xero - ID: ${newContactId}, Name: ${contactName}`
				);
				return newContactId;
			}
		} catch (error: any) {
			console.error(`Failed to create contact in Xero: ${error.message}`);
		}
	}

	// If contact creation fails due to missing name information
	console.error(
		`Skipping invoice ${hsInvoice.id} - Contact creation failed due to missing name information`
	);
	await logSyncResult(
		hsInvoice.id,
		'error',
		'Contact creation failed - missing required contact information'
	);
	return null;
}

// 4. Enhanced Product/Line Item Validation in Xero with ItemCode mapping
async function validateAndCreateProductsInXero(
	hsInvoice: IHubSpotInvoice,
	connection: any
): Promise<boolean> {
	console.log(`Validating products in Xero for invoice ${hsInvoice.id}...`);

	if (!hsInvoice.lineItems || hsInvoice.lineItems.length === 0) {
		console.log(`No line items to validate for invoice ${hsInvoice.id}`);
		return true; // No products to validate, consider success
	}

	// Process each line item to ensure proper ItemCode mapping using hs_product_id
	for (const lineItem of hsInvoice.lineItems) {
		if (!lineItem.description || lineItem.description.trim().length === 0) {
			console.log(`Skipping line item with no description`);
			continue;
		}

		const productName = lineItem.description.trim();
		const hsProductId = lineItem.hsProductId; // Get hs_product_id from HubSpot

		if (!hsProductId) {
			console.log(`Skipping line item ${productName} - no hs_product_id found`);
			continue;
		}

		// Create consistent ItemCode using hs_product_id
		const itemCode = generateItemCode(hsProductId, productName);

		console.log(
			`Processing line item: ${productName}, hs_product_id: ${hsProductId}`
		);

		// Check if product exists in Xero by ItemCode
		let existingProduct = null;

		try {
			const productResponse = await xeroApiCall(
				'get',
				`/Items?where=Code%3D%22${encodeURIComponent(itemCode)}%22`,
				connection
			);
			if (
				productResponse?.data?.Items &&
				productResponse.data.Items.length > 0
			) {
				existingProduct = productResponse.data.Items[0];
				console.log(
					`Found existing product by ItemCode ${itemCode}: ${existingProduct.Name}`
				);
				// Update the line item with the ItemCode
				lineItem.itemCode = itemCode;
				continue; // Product exists, no need to create
			}
		} catch (error) {
			console.log(
				`Could not find product by ItemCode ${itemCode}, will create new product`
			);
		}

		// Product doesn't exist, create it in Xero
		console.log(
			`Creating new product in Xero: ${productName} with ItemCode: ${itemCode}`
		);

		const productPayload = {
			Code: itemCode,
			Name: productName,
			Description: productName,
			SalesDetails: {
				UnitPrice: lineItem.unitPrice || 0,
				AccountCode: XERO_ACCOUNT_CODE,
				TaxType: XERO_TAXTYPE_CODE,
			},
		};

		try {
			const createResponse = await xeroApiCall('post', '/Items', connection, {
				Items: [productPayload],
			});

			if (
				createResponse?.data?.Items &&
				Array.isArray(createResponse.data.Items) &&
				createResponse.data.Items.length > 0
			) {
				const createdProduct = createResponse.data.Items[0];
				console.log(
					`Product created in Xero: ${createdProduct.ItemID} with Code: ${createdProduct.Code}`
				);

				// Update the line item with the created ItemCode
				lineItem.itemCode = createdProduct.Code;
			} else {
				console.error(
					`Failed to create product ${productName} in Xero: Invalid response`
				);
				return false;
			}
		} catch (error: any) {
			console.error(
				`Failed to create product ${productName} in Xero:`,
				error.message
			);
			return false;
		}
	}

	console.log(
		`All products validated/created successfully for invoice ${hsInvoice.id}`
	);
	return true;
}

// Helper function to get existing products from Xero by name
async function getExistingXeroProductsByName(
	productNames: string[],
	connection: any
): Promise<any[]> {
	// For performance, process products in batches
	const batchSize = 10;
	let allProducts: any[] = [];

	for (let i = 0; i < productNames.length; i += batchSize) {
		const batch = productNames.slice(i, i + batchSize);
		const whereClause = batch.map((name) => `Name=="${name}"`).join(' OR ');

		const response = await xeroApiCall(
			'get',
			`/Items?where=${encodeURIComponent(whereClause)}`,
			connection
		);
		const products = response?.data?.Items || [];
		allProducts = [...allProducts, ...products];
	}

	return allProducts;
}

// 5. Enhanced Xero Invoice Creation with Existing Invoice Handling
async function createInvoiceInXero(
	hsInvoice: IHubSpotInvoice,
	contactId: string,
	connection: any,
	synclogId?: string
): Promise<void> {
	console.log(
		`Processing invoice in Xero for HubSpot invoice ${hsInvoice.id}...`
	);

	if (synclogId) {
		await logSyncHistory(
			synclogId,
			'pending',
			`Processing invoice in Xero for HubSpot invoice #: ${hsInvoice.id}`
		);
	}

	try {
		// First, check if invoice already exists in Xero
		const existingXeroInvoice = await findExistingXeroInvoice(
			hsInvoice,
			connection
		);

		if (existingXeroInvoice) {
			console.log(
				`Found existing Xero invoice ${existingXeroInvoice.InvoiceID} for HubSpot invoice ${hsInvoice.id}`
			);

			// Log to sync history that we found existing Xero invoice
			if (synclogId) {
				await logSyncHistory(
					synclogId,
					'pending',
					`Found existing Xero invoice ${existingXeroInvoice.InvoiceID} (Status: ${existingXeroInvoice.Status}) for HubSpot invoice ${hsInvoice.id}`
				);
			}

			// Handle existing invoice based on status and differences
			await handleExistingInvoiceWithEnhancedLogic(
				hsInvoice,
				existingXeroInvoice,
				contactId,
				connection,
				synclogId
			);
		} else {
			console.log(
				`No existing invoice found, creating new invoice for HubSpot invoice ${hsInvoice.id}`
			);

			// Create new invoice
			await createNewXeroInvoice(hsInvoice, contactId, connection, synclogId);
		}
	} catch (error: any) {
		console.error(`Error processing invoice in Xero: ${error.message}`);

		if (synclogId) {
			await logSyncHistory(
				synclogId,
				'error',
				`Error processing invoice in Xero: ${error.message}`,
				{ error: error.message, stack: error.stack }
			);
		}

		await logSyncResult(
			hsInvoice.id,
			'error',
			`Invoice processing error: ${error.message}`
		);
		throw error;
	}
}

// Create new Xero invoice
async function createNewXeroInvoice(
	hsInvoice: IHubSpotInvoice,
	contactId: string,
	connection: any,
	synclogId?: string
): Promise<void> {
	console.log(
		`Creating new invoice in Xero for HubSpot invoice ${hsInvoice.id}...`
	);

	if (synclogId) {
		await logSyncHistory(
			synclogId,
			'pending',
			`Creating new invoice in Xero for HubSpot invoice #: ${hsInvoice.id}`
		);
	}

	// Use the validated contact ID and existing/newly created product IDs
	const invoiceResult = await createXeroInvoice(
		hsInvoice,
		contactId,
		connection,
		synclogId
	);

	// Check if invoice was skipped due to no line items
	if (
		invoiceResult.Status === 400 &&
		invoiceResult.Message.includes('no line items')
	) {
		console.log(
			`Invoice ${hsInvoice.id} skipped due to no line items. Continuing with next invoice.`
		);

		// Log the skip in sync results
		await logSyncResult(
			hsInvoice.id,
			'error',
			`Skipped: ${invoiceResult.Message}`
		);

		return; // Skip this invoice and continue with the next one
	}

	if (invoiceResult.InvoiceID && invoiceResult.Status === 200) {
		// Get the Xero invoice number from the created invoice
		const xeroInvoice = await getXeroInvoiceById(
			invoiceResult.InvoiceID,
			connection
		);
		const xeroInvoiceNumber = xeroInvoice?.InvoiceNumber || 'Unknown';

		console.log(
			`Invoice created in Xero - ID: ${invoiceResult.InvoiceID}, Number: ${xeroInvoiceNumber}`
		);

		// Update database with Xero invoice details
		await updateSyncLogWithXeroId(
			hsInvoice.id,
			invoiceResult.InvoiceID,
			connection,
			hsInvoice
		);

		// Log successful creation
		await logSyncResult(
			hsInvoice.id,
			'success',
			`Invoice created in Xero - ID: ${invoiceResult.InvoiceID}, Number: ${xeroInvoiceNumber}`
		);

		if (synclogId) {
			await logSyncHistory(
				synclogId,
				'success',
				`Invoice created in Xero - ID: ${invoiceResult.InvoiceID}, Number: ${xeroInvoiceNumber}`
			);
		}

		// Handle payment synchronization
		await handleInvoicePaymentSync(
			invoiceResult.InvoiceID,
			hsInvoice,
			connection,
			synclogId
		);
	} else {
		const errorMessage = `Failed to create invoice in Xero: ${invoiceResult.Message}`;
		console.error(errorMessage);

		await logSyncResult(
			hsInvoice.id,
			'error',
			`Invoice creation failed: ${invoiceResult.Message}`
		);

		if (synclogId) {
			await logSyncHistory(synclogId, 'error', errorMessage, { invoiceResult });
		}
	}
}

async function handleExistingInvoiceWithEnhancedLogic(
	hsInvoice: IHubSpotInvoice,
	xeroInvoice: any,
	contactId: string,
	connection: any,
	synclogId?: string
): Promise<void> {
	console.log(
		`Handling existing Xero invoice ${xeroInvoice.InvoiceID} (Status: ${xeroInvoice.Status}) for HubSpot invoice ${hsInvoice.id}`
	);

	if (synclogId) {
		await logSyncHistory(
			synclogId,
			'pending',
			`Analyzing existing Xero invoice ${xeroInvoice.InvoiceID} (Status: ${xeroInvoice.Status})`
		);
	}

	// 1. Compare invoice details to check for differences FIRST (before status-based decisions)
	console.log(
		` Comparing HubSpot invoice ${hsInvoice.id} with Xero invoice ${xeroInvoice.InvoiceID}...`
	);
	const comparison = compareInvoiceDetails(hsInvoice, xeroInvoice);

	if (!comparison.hasChanges) {
		// No changes detected - skip processing regardless of invoice status
		console.log(
			` Invoice ${hsInvoice.id} matches existing Xero invoice ${xeroInvoice.InvoiceID}. No changes needed.`
		);

		if (synclogId) {
			await logSyncHistory(
				synclogId,
				'success',
				`Invoice matches existing Xero invoice ${xeroInvoice.InvoiceID}. No changes detected - skipping processing.`
			);
		}

		// Update database with existing Xero invoice details
		await updateSyncLogWithXeroId(
			hsInvoice.id,
			xeroInvoice.InvoiceID,
			connection,
			hsInvoice
		);

		await logSyncResult(
			hsInvoice.id,
			'success',
			`Invoice matches existing Xero invoice ${xeroInvoice.InvoiceID} - no changes needed`
		);

		return; // Exit early - no processing needed
	}

	// Changes detected - log the differences
	console.log(
		` Changes detected for invoice ${hsInvoice.id}:`,
		comparison.changes
	);

	if (synclogId) {
		await logSyncHistory(
			synclogId,
			'pending',
			`Changes detected: ${comparison.changes.join('; ')}`,
			{
				changes: comparison.changes,
				lineItemChanges: comparison.lineItemChanges,
			}
		);
	}

	// 2. Pre-Operation Invoice Status Validation (after confirming changes exist)
	const statusValidation = await validateXeroInvoiceStatus(
		xeroInvoice,
		connection,
		synclogId
	);

	// Check if invoice is non-editable and needs void-and-recreate
	if (!statusValidation.isEditable) {
		const invoiceNumber = xeroInvoice.InvoiceNumber || 'Unknown';
		const statusMessage = `Invoice ${invoiceNumber} in Xero is ${statusValidation.status}. Cannot be updated.`;

		console.log(statusMessage);

		if (synclogId) {
			await logSyncHistory(synclogId, 'pending', statusMessage);
		}

		// Proceed with void-and-recreate since changes were detected
		console.log(
			` Invoice ${invoiceNumber} has changes and is ${statusValidation.status}. Proceeding with void-and-recreate.`
		);

		if (synclogId) {
			await logSyncHistory(
				synclogId,
				'pending',
				`Invoice ${invoiceNumber} has changes and is ${statusValidation.status}. Proceeding with void-and-recreate.`
			);
		}

		// Trigger void-and-recreate flow for non-editable status with changes
		await handleVoidAndRecreateFlow(
			hsInvoice,
			xeroInvoice,
			contactId,
			connection,
			synclogId,
			'non-editable-status-with-changes'
		);
		return;
	}

	// 3. Pre-check payments/credits
	const invoiceStatus = xeroInvoice.Status;

	console.log(
		` Pre-checking payments/credits for invoice ${xeroInvoice.InvoiceID} (Status: ${invoiceStatus})`
	);

	// Check for payments/credits
	const hasPaymentsOrCredits = await checkInvoiceHasPaymentsOrCredits(
		xeroInvoice.InvoiceID,
		connection,
		xeroInvoice
	);

	if (synclogId) {
		await logSyncHistory(
			synclogId,
			'pending',
			`Invoice ${xeroInvoice.InvoiceID} has payments/credits: ${hasPaymentsOrCredits}, Status: ${invoiceStatus}`
		);
	}

	// Simplified routing: Use void-and-recreate for ANY invoice with payments/credits OR non-editable status
	if (
		hasPaymentsOrCredits ||
		invoiceStatus === 'PAID' ||
		!canEditXeroInvoice(invoiceStatus)
	) {
		// Route to void-and-recreate flow for:
		// 1. Invoices with payments/credits (regardless of status)
		// 2. PAID invoices (even without payments - safer approach)
		// 3. Non-editable invoices (SUBMITTED, VOIDED, etc.)

		const reason = hasPaymentsOrCredits
			? 'has-payments-or-credits'
			: invoiceStatus === 'PAID'
			? 'paid-status'
			: 'non-editable-status';

		console.log(
			` Invoice ${xeroInvoice.InvoiceID} (${invoiceStatus}) routing to void-and-recreate flow (reason: ${reason})`
		);

		if (synclogId) {
			await logSyncHistory(
				synclogId,
				'pending',
				`Routing to void-and-recreate flow - Status: ${invoiceStatus}, Has payments/credits: ${hasPaymentsOrCredits}, Reason: ${reason}`
			);
		}

		await handleVoidAndRecreateFlow(
			hsInvoice,
			xeroInvoice,
			contactId,
			connection,
			synclogId,
			reason,
			hasPaymentsOrCredits // Pass the pre-checked payment/credit status
		);
	} else {
		// Only truly editable invoices without payments/credits go to direct edit
		console.log(
			` Invoice ${xeroInvoice.InvoiceID} (${invoiceStatus}) has no payments/credits - using direct edit`
		);

		await handleEditableInvoiceChanges(
			hsInvoice,
			xeroInvoice,
			contactId,
			connection,
			synclogId
		);
	}
}

// REMOVED: handlePaidInvoiceChanges function - now using unified handleVoidAndRecreateFlow
// All paid invoices now route through handleVoidAndRecreateFlow for consistency

// Handle changes to editable invoices (DRAFT or SUBMITTED status)
async function handleEditableInvoiceChanges(
	hsInvoice: IHubSpotInvoice,
	xeroInvoice: any,
	contactId: string,
	connection: any,
	synclogId?: string
): Promise<void> {
	console.log(
		`Updating editable invoice ${xeroInvoice.InvoiceID} (Status: ${xeroInvoice.Status}) for HubSpot invoice ${hsInvoice.id}`
	);

	if (synclogId) {
		await logSyncHistory(
			synclogId,
			'pending',
			`Updating editable invoice ${xeroInvoice.InvoiceID} with changes from HubSpot`
		);
	}

	try {
		// Update the existing invoice with new data
		await updateXeroInvoice(xeroInvoice.InvoiceID, hsInvoice, connection);

		// Update database with existing invoice details
		await updateSyncLogWithXeroId(
			hsInvoice.id,
			xeroInvoice.InvoiceID,
			connection,
			hsInvoice
		);

		const successMessage = `Updated editable invoice ${xeroInvoice.InvoiceID} with changes from HubSpot invoice ${hsInvoice.id}`;
		console.log(successMessage);

		await logSyncResult(hsInvoice.id, 'success', successMessage);

		if (synclogId) {
			await logSyncHistory(synclogId, 'success', successMessage);
		}

		// Handle payment synchronization after updating the invoice
		await handleInvoicePaymentSync(
			xeroInvoice.InvoiceID,
			hsInvoice,
			connection,
			synclogId
		);
	} catch (error: any) {
		// Check if this is a status-related error that should trigger void-and-recreate
		if ((error as any).isStatusError) {
			const detectedStatus = (error as any).detectedStatus;
			const invoiceNumber = xeroInvoice.InvoiceNumber || 'Unknown';

			console.log(
				`Invoice ${invoiceNumber} became ${detectedStatus} during update. Triggering void-and-recreate flow.`
			);

			if (synclogId) {
				await logSyncHistory(
					synclogId,
					'pending',
					`Invoice ${invoiceNumber} became ${detectedStatus} during update. Triggering void-and-recreate flow.`
				);
			}

			// Trigger void-and-recreate flow for status errors
			try {
				await handleVoidAndRecreateFlow(
					hsInvoice,
					xeroInvoice,
					contactId,
					connection,
					synclogId,
					'status-error-during-update'
				);
				return; // Successfully handled via void-and-recreate
			} catch (recreateError: any) {
				const recreateErrorMessage = `Failed to handle status error via void-and-recreate: ${recreateError.message}`;
				console.error(recreateErrorMessage);

				await logSyncResult(hsInvoice.id, 'error', recreateErrorMessage);

				if (synclogId) {
					await logSyncHistory(synclogId, 'error', recreateErrorMessage, {
						originalError: error.message,
						recreateError: recreateError.message,
					});
				}

				throw recreateError;
			}
		}

		// Handle non-status errors normally
		const errorMessage = `Error updating editable invoice: ${error.message}`;
		console.error(errorMessage);

		await logSyncResult(hsInvoice.id, 'error', errorMessage);

		if (synclogId) {
			await logSyncHistory(synclogId, 'error', errorMessage, {
				error: error.message,
				invoiceId: xeroInvoice.InvoiceID,
				stack: error.stack,
			});
		}
		throw error;
	}
}

// REMOVED: handleVoidableInvoiceChanges function - now using unified handleVoidAndRecreateFlow
// All voidable invoices now route through handleVoidAndRecreateFlow for consistency

// Helper function to update sync log with Xero invoice ID
async function updateSyncLogWithXeroId(
	hsInvoiceId: string,
	xeroInvoiceId: string,
	connection: any,
	hsInvoice?: IHubSpotInvoice
): Promise<void> {
	try {
		// Find existing invoice record
		const existingInvoice = await prisma.invoices.findFirst({
			where: { hubSpotInvoiceId: hsInvoiceId },
		});

		if (existingInvoice) {
			// Update existing record
			await prisma.invoices.update({
				where: { id: existingInvoice.id },
				data: {
					xeroInvoiceId: xeroInvoiceId,
					updatedAt: new Date(),
				},
			});
		} else {
			// Create new record - need to create synclog first
			if (!connection)
				throw new ApiException({
					status: 500,
					message: 'No connection found',
					errorDescription:
						'Critical error: Connection object is null or undefined',
				});

			const syncLog = await prisma.synclog.create({
				data: {
					connectionId: connection.id,
					hubspotInvoiceId: hsInvoiceId,
					xeroInvoiceId: xeroInvoiceId,
					syncStatus: 'success',
				},
			});

			// Create invoice record with available data
			const invoiceData: any = {
				hubSpotInvoiceId: hsInvoiceId,
				xeroInvoiceId: xeroInvoiceId,
				synclogId: syncLog.id,
			};

			// Add additional fields if hsInvoice data is available
			if (hsInvoice) {
				// Fetch quote ID and deal ID from invoice associations
				const quoteId = await getQuoteIdForInvoice(hsInvoice.id, connection);
				const dealId = await getDealIdForInvoice(hsInvoice.id, connection);

				invoiceData.wooCommerceAmount =
					hsInvoice?.properties?.hs_amount_billed?.toString() ?? null;
				invoiceData.hubSpotQuoteId = quoteId ?? null;
				invoiceData.hubSpotInvoiceNumber =
					hsInvoice?.properties?.hs_number ?? null;
				invoiceData.invoiceStatus =
					hsInvoice?.properties?.hs_invoice_status ?? 'PENDING';
				invoiceData.hubSpotContactId = hsInvoice.contactId ?? null;
				invoiceData.hubSpotDealId = dealId ?? null; // Use the fetched dealId from associations
			}

			await prisma.invoices.create({
				data: invoiceData,
			});
		}

		console.log(
			`Updated sync log for HubSpot invoice ${hsInvoiceId} with Xero invoice ${xeroInvoiceId}`
		);
	} catch (error: any) {
		console.error(`Error updating sync log:`, error.message);
	}
}

// Helper function to update existing Xero invoice
async function updateXeroInvoice(
	xeroInvoiceId: string,
	hsInvoice: IHubSpotInvoice,
	connection: any
): Promise<void> {
	if (!connection?.xeroCredentials)
		throw new ApiException({
			status: 500,
			message: 'Missing Xero credentials',
			errorDescription:
				'Critical error: Xero credentials not found in connection object',
		});

	// Get line items from database instead of relying on HubSpot object
	let lineItems: any[] = [];

	try {
		// Fetch the invoice with line items from database
		const dbInvoice = await prisma.invoices.findFirst({
			where: { hubSpotInvoiceId: hsInvoice.id },
			include: { InvoiceLines: true },
		});

		if (dbInvoice?.InvoiceLines && dbInvoice.InvoiceLines.length > 0) {
			console.log(
				`Using ${dbInvoice.InvoiceLines.length} line items from database for updating Xero invoice ${xeroInvoiceId}`
			);

			// Use line items from database
			lineItems = dbInvoice.InvoiceLines.map((item) => ({
				Description: item.description || item.productName || 'Service',
				itemCode: generateItemCode(
					item.hubSpotProductId,
					item.description || item.productName
				),
				Quantity: item.quantity || 1,
				UnitAmount: item.unitPrice || 0,
				AccountCode: XERO_ACCOUNT_CODE,
				TaxType: XERO_TAXTYPE_CODE,
			}));
		} else {
			console.log(
				`No line items found in database for invoice ${hsInvoice.id}, using HubSpot object for update`
			);

			// Fallback to HubSpot line items
			lineItems =
				hsInvoice.lineItems?.map((item) => ({
					Description: item.description,
					ItemCode: generateItemCode(item.hsProductId, item.description),
					Quantity: item.quantity,
					UnitAmount: item.unitPrice,
					AccountCode: XERO_ACCOUNT_CODE,
					TaxType: XERO_TAXTYPE_CODE,
				})) || [];
		}
	} catch (error: any) {
		console.error(
			`Error fetching line items from database for updating invoice ${hsInvoice.id}:`,
			error.message
		);

		// Fallback to HubSpot line items
		lineItems =
			hsInvoice.lineItems?.map((item) => ({
				Description: item.description,
				ItemCode: generateItemCode(item.hsProductId, item.description),
				Quantity: item.quantity,
				UnitAmount: item.unitPrice,
				AccountCode: XERO_ACCOUNT_CODE,
				TaxType: XERO_TAXTYPE_CODE,
			})) || [];
	}

	// Log the update payload for debugging
	console.log(
		`Updating Xero invoice ${xeroInvoiceId} with ${lineItems.length} line items`
	);
	console.log(`Line items being sent:`, JSON.stringify(lineItems, null, 2));

	const updatePayload = {
		LineItems: lineItems,
		Status: 'AUTHORISED',
	};

	try {
		await xeroApiCall(
			'post',
			`/Invoices/${xeroInvoiceId}`,
			connection,
			updatePayload
		);

		console.log(`Updated Xero invoice ${xeroInvoiceId}`);
	} catch (error: any) {
		// 3. Robust Error Handling for Status-Related Errors
		const errorMessage = error.message || '';
		const isStatusError =
			errorMessage.includes('has been deleted and cannot be modified') ||
			errorMessage.includes('has been voided and cannot be modified') ||
			errorMessage.includes('has been paid and cannot be modified') ||
			errorMessage.includes('cannot be modified') ||
			error.response?.status === 400;

		if (isStatusError) {
			console.log(
				`Detected status-related error for invoice ${xeroInvoiceId}: ${errorMessage}`
			);

			// Parse error message to extract status information
			let detectedStatus = 'UNKNOWN';
			if (errorMessage.includes('deleted')) detectedStatus = 'DELETED';
			else if (errorMessage.includes('voided')) detectedStatus = 'VOIDED';
			else if (errorMessage.includes('paid')) detectedStatus = 'PAID';

			console.log(
				`Detected status issue: ${detectedStatus}. This should trigger void-and-recreate flow.`
			);

			// Re-throw with enhanced error information for upstream handling
			const enhancedError = new Error(
				`Invoice ${xeroInvoiceId} is ${detectedStatus} and cannot be modified`
			);
			(enhancedError as any).isStatusError = true;
			(enhancedError as any).detectedStatus = detectedStatus;
			(enhancedError as any).originalError = error;
			throw enhancedError;
		}

		// Re-throw non-status errors
		throw error;
	}
}

async function getXeroInvoiceById(invoiceId: string, connection: any) {
	const response = await xeroApiCall(
		'get',
		`/Invoices/${invoiceId}`,
		connection
	);
	return response?.data?.Invoices?.[0] || null;
}

export async function handleExistingInvoice(
	hs: IHubSpotInvoice,
	xeroInvoiceId: string,
	connection: any
): Promise<void> {
	const xeroInvoice = await getXeroInvoiceById(xeroInvoiceId, connection);
	if (!xeroInvoice) {
		// Invoice ID exists in DB but not found in Xero → Create new
		return await handleNewInvoice(hs, connection);
	}

	const lineItemsMatch = xeroInvoice.LineItems.every(
		(xLine: any, index: number) => {
			const hsLine = hs.lineItems[index];
			return (
				xLine.Description === hsLine.description &&
				Number(xLine.Quantity) === hsLine.quantity &&
				Number(xLine.UnitAmount) === hsLine.unitPrice
			);
		}
	);

	if (
		xeroInvoice.Status === 'PAID' &&
		Number(xeroInvoice.Total) === hs.amount &&
		lineItemsMatch
	) {
		return logSyncResult(hs.id, 'error', 'Paid invoice matches.');
	}

	// If invoice is not editable (e.g. PAID or not in draft), recreate
	if (xeroInvoice.Status !== 'DRAFT') {
		await deleteXeroPayments(xeroInvoice.InvoiceID, connection);
		await voidXeroInvoice(xeroInvoice.InvoiceID, connection);

		await prisma.invoices.deleteMany({
			where: { xeroInvoiceId: xeroInvoice.InvoiceID },
		});
		return await handleNewInvoice(hs, connection);
	}

	// If editable → Update invoice
	if (!connection?.xeroCredentials)
		throw new ApiException({
			status: 500,
			message: 'Missing Xero credentials',
			errorDescription:
				'Critical error: Xero credentials not found in connection object',
		});
	const { access_token } = connection.xeroCredentials as any;

	const lineItems = hs.lineItems.map((item) => ({
		Description: item.description,
		ItemCode: generateItemCode(item.hsProductId, item.description),
		Quantity: item.quantity,
		UnitAmount: item.unitPrice,
		AccountCode: XERO_ACCOUNT_CODE,
		TaxType: XERO_TAXTYPE_CODE,
	}));

	await axios.post(
		`${XERO_API_BASE}/Invoices/${xeroInvoice.InvoiceID}`,
		{
			LineItems: lineItems,
			Status: 'AUTHORISED',
		},
		{
			headers: {
				Authorization: `Bearer ${access_token}`,
				'Xero-tenant-id': getXeroTenantId(connection),
				Accept: 'application/json',
				'Content-Type': 'application/json',
			},
		}
	);

	await logSyncResult(hs.id, 'success', `Invoice updated in Xero`);
}

async function handleNewInvoice(hs: IHubSpotInvoice, connection: any) {
	const contactId = await resolveContact(hs, connection);

	// Skip invoice if contact resolution failed
	if (!contactId || contactId.trim() === '') {
		console.error(`Skipping invoice ${hs.id} - Contact resolution failed`);
		console.error(
			`   - No valid contact name found (missing company name, first name, and last name)`
		);
		await logSyncResult(
			hs.id,
			'error',
			'Contact resolution failed - missing required contact information'
		);
		return;
	}

	console.log(
		`Contact resolved for invoice ${hs.id}, proceeding with Xero invoice creation`
	);
	const invoice = await createXeroInvoice(hs, contactId, connection);

	// Check if invoice was skipped due to no line items
	if (invoice.Status === 400 && invoice.Message.includes('no line items')) {
		console.log(
			`Invoice ${hs.id} skipped in handleNewInvoice due to no line items.`
		);

		await logSyncResult(hs.id, 'error', `Skipped: ${invoice.Message}`);

		return; // Skip this invoice
	}

	// Use enhanced payment sync logic
	await handleInvoicePaymentSync(invoice.InvoiceID, hs, connection);

	await logSyncResult(
		hs.id,
		'success',
		`Created new invoice ${invoice.InvoiceID}`
	);
}

// Add this function to get primary company ID
async function getPrimaryCompanyId(
	hubSpotContactId: string,
	hApiKey: string
): Promise<string | null> {
	try {
		const response = await axios.get(
			`https://api.hubapi.com/crm/v3/objects/contacts/${hubSpotContactId}/associations/companies`,
			{
				headers: {
					Authorization: `Bearer ${hApiKey}`,
					'Content-Type': 'application/json',
				},
			}
		);

		const companyAssociations = response.data?.results;

		if (!companyAssociations || companyAssociations.length === 0) {
			return null;
		}

		return companyAssociations[0]?.id || null;
	} catch (error: any) {
		console.error(`Error fetching primary company ID: ${error.message}`);
		return null;
	}
}

// Search for existing invoice in Xero by Reference
async function findXeroInvoiceByReference(
	hubspotInvoiceId: string,
	connection: any
) {
	try {
		const response = await xeroApiCall(
			'get',
			`/Invoices?where=Reference="${hubspotInvoiceId}"&page=1`,
			connection
		);
		return response?.data?.Invoices?.[0] || null;
	} catch (error: any) {
		console.error(
			`Error searching for Xero invoice by reference ${hubspotInvoiceId}:`,
			error.message
		);
		return null;
	}
}

// Search for existing invoice in Xero by Invoice Number
async function findXeroInvoiceByNumber(invoiceNumber: string, connection: any) {
	try {
		const response = await xeroApiCall(
			'get',
			`/Invoices?where=InvoiceNumber="${invoiceNumber}"&page=1`,
			connection
		);
		return response?.data?.Invoices?.[0] || null;
	} catch (error: any) {
		console.error(
			`Error searching for Xero invoice by number ${invoiceNumber}:`,
			error.message
		);
		return null;
	}
}

async function findExistingXeroInvoice(
	hsInvoice: IHubSpotInvoice,
	connection: any
): Promise<any> {
	// First try to find by reference, try to find by invoice number
	// Try to find invoice number from database using hsInvoice.id as reference
	const dbInvoice = await prisma.invoices.findFirst({
		where: { hubSpotInvoiceId: hsInvoice.id },
		select: { xeroInvoiceNumber: true },
	});
	const invoiceNumber = dbInvoice?.xeroInvoiceNumber;
	let existingInvoice;
	if (invoiceNumber) {
		existingInvoice = await findXeroInvoiceByNumber(invoiceNumber, connection);
		if (existingInvoice) {
			console.log(
				`Found existing Xero invoice by number: ${existingInvoice.InvoiceID} (${invoiceNumber})`
			);
			return existingInvoice;
		}
	}

	// If not found by find by HubSpot invoice ID (Reference field)
	existingInvoice = await findXeroInvoiceByReference(hsInvoice.id, connection);

	if (existingInvoice) {
		console.log(
			`Found existing Xero invoice by reference: ${existingInvoice.InvoiceID}`
		);
		return existingInvoice;
	}

	return null;
}

// Check if a Xero invoice can be edited based on its status
function canEditXeroInvoice(invoiceStatus: string): boolean {
	const editableStatuses = ['DRAFT', 'SUBMITTED'];
	return editableStatuses.includes(invoiceStatus);
}

// Check if a Xero invoice can be voided
function canVoidXeroInvoice(invoiceStatus: string): boolean {
	const voidableStatuses = ['SUBMITTED', 'AUTHORISED'];
	return voidableStatuses.includes(invoiceStatus);
}

// Enhanced function to compare invoice details for differences
function compareInvoiceDetails(
	hsInvoice: IHubSpotInvoice,
	xeroInvoice: any
): {
	hasChanges: boolean;
	changes: string[];
	lineItemChanges: any[];
} {
	const changes: string[] = [];
	const lineItemChanges: any[] = [];

	// Compare invoice status - CRITICAL: VOIDED invoices should always be recreated if HubSpot invoice is not voided
	const xeroStatus = xeroInvoice.Status;
	const hsStatus = hsInvoice?.properties?.hs_invoice_status || 'UNKNOWN';

	// If Xero invoice is VOIDED but HubSpot invoice is not VOID/CANCELLED, this is a significant change
	if (xeroStatus === 'VOIDED' && !['VOID', 'CANCELLED'].includes(hsStatus)) {
		changes.push(
			`Status: Xero is VOIDED but HubSpot is ${hsStatus} - recreation needed`
		);
	}

	// If Xero invoice is DELETED, it should always be recreated
	if (xeroStatus === 'DELETED') {
		changes.push(`Status: Xero invoice is DELETED - recreation needed`);
	}

	// Compare total amount
	const hsAmount =
		hsInvoice?.properties?.hs_amount_billed || hsInvoice.amount || 0;
	const xeroAmount = parseFloat(xeroInvoice.Total) || 0;
	if (Math.abs(hsAmount - xeroAmount) > 0.01) {
		changes.push(`Amount: HubSpot ${hsAmount} vs Xero ${xeroAmount}`);
	}

	// Compare line items
	const hsLineItems = hsInvoice.lineItems || [];
	const xeroLineItems = xeroInvoice.LineItems || [];

	// Check if line item count differs
	if (hsLineItems.length !== xeroLineItems.length) {
		changes.push(
			`Line item count: HubSpot ${hsLineItems.length} vs Xero ${xeroLineItems.length}`
		);
	}

	// Compare individual line items
	const maxItems = Math.max(hsLineItems.length, xeroLineItems.length);
	for (let i = 0; i < maxItems; i++) {
		const hsItem = hsLineItems[i];
		const xeroItem = xeroLineItems[i];

		if (!hsItem && xeroItem) {
			lineItemChanges.push({
				index: i,
				type: 'removed',
				description: `Xero item "${xeroItem.Description}" not found in HubSpot`,
			});
		} else if (hsItem && !xeroItem) {
			lineItemChanges.push({
				index: i,
				type: 'added',
				description: `HubSpot item "${hsItem.description}" not found in Xero`,
			});
		} else if (hsItem && xeroItem) {
			const itemChanges: string[] = [];

			if (hsItem.description !== xeroItem.Description) {
				itemChanges.push(
					`description: "${hsItem.description}" vs "${xeroItem.Description}"`
				);
			}

			if (
				Math.abs(
					(hsItem.quantity || 1) - (parseFloat(xeroItem.Quantity) || 1)
				) > 0.001
			) {
				itemChanges.push(
					`quantity: ${hsItem.quantity} vs ${xeroItem.Quantity}`
				);
			}

			if (
				Math.abs(
					(hsItem.unitPrice || 0) - (parseFloat(xeroItem.UnitAmount) || 0)
				) > 0.01
			) {
				itemChanges.push(
					`unit price: ${hsItem.unitPrice} vs ${xeroItem.UnitAmount}`
				);
			}

			if (itemChanges.length > 0) {
				lineItemChanges.push({
					index: i,
					type: 'modified',
					description: `Line item ${i + 1}: ${itemChanges.join(', ')}`,
				});
			}
		}
	}

	// Add line item changes to main changes array
	if (lineItemChanges.length > 0) {
		changes.push(`Line items modified: ${lineItemChanges.length} changes`);
	}

	return {
		hasChanges: changes.length > 0,
		changes,
		lineItemChanges,
	};
}
