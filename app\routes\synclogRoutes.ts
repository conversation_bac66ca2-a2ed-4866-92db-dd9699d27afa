import express from 'express';
import { IQuery, RequestExtended } from '../interfaces/global';
import { synclogService } from '../services/synclogService';
import asyncHandler from '../utils/async-handler';
import {
	syncHubSpotInvoicesToXero,
	getSyncStatus,
} from '../services/syncHubspotXero';

const router = express.Router();

router.get(
	'/',
	asyncHandler(async (req: RequestExtended) => {
		return synclogService.getSynclogs(req.query as IQuery);
	})
);

router.get(
	'/:id',
	asyncHandler(async (req: RequestExtended) => {
		return synclogService.getSyncLogHistoryById(req.params.id);
	})
);

router.get(
	'/cogs',
	asyncHandler(async (req: RequestExtended) => {
		return synclogService.getCOGSSynclogs(req.query as IQuery);
	})
);

router.get(
	'/b2c/synclogs',
	asyncHandler(async (req: RequestExtended) => {
		return synclogService.getB2CSynclogs(req.query as IQuery);
	})
);

router.get(
	'/b2c/:id',
	asyncHandler(async (req: RequestExtended) => {
		return synclogService.getB2CSyncLogHistoryById(req.params.id);
	})
);

router.get(
	'/shopify/orders',
	asyncHandler(async (req: RequestExtended) => {
		return synclogService.getOrdersByPaymentGatewayAndDate(req.query as IQuery);
	})
);

router.get(
	'/invoices/hubspot-xero',
	asyncHandler(async (req: RequestExtended) => {
		const query = req.query as {
			fromDate?: string;
			toDate?: string;
			page?: number;
		};

		// Start the sync process in the background
		const syncPromise = syncHubSpotInvoicesToXero(query);

		// Return immediately with a response indicating sync has started
		// Don't wait for the sync to complete to avoid gateway timeouts
		setImmediate(() => {
			syncPromise.catch((error) => {
				console.error('Background sync failed:', error);
				// Emit error via socket if available
				if ((global as any).io) {
					(global as any).io.emit('sync-error', {
						message: 'HubSpot to Xero sync failed',
						error: error.message,
						timestamp: new Date().toISOString(),
					});
				}
			});
		});

		return {
			message: 'HubSpot to Xero sync started in background',
			status: 'started',
			timestamp: new Date().toISOString(),
			query: query,
		};
	})
);

router.get(
	'/invoices/hubspot-xero/status',
	asyncHandler(async () => {
		const status = getSyncStatus();

		return {
			syncInProgress: status.inProgress,
			startTime: status.startTime,
			runningTimeSeconds: status.runningTimeSeconds,
			timestamp: new Date().toISOString(),
		};
	})
);

export default router;
